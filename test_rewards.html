<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы наград</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-button {
            background: #333;
            border: 1px solid #555;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
            border-radius: 5px;
            position: relative;
        }
        .test-button:hover {
            background: #444;
        }
        .reward-badge {
            position: absolute !important;
            right: 16px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            width: 36px !important;
            height: 36px !important;
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%) !important;
            border: 2px solid #e0e0e0 !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 12px !important;
            font-weight: bold !important;
            color: #333333 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        .log {
            background: #222;
            border: 1px solid #444;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🏆 Тест системы наград</h1>
    
    <div>
        <h2>Тестовые кнопки:</h2>
        <button id="openLinkButton" class="test-button">
            Открыть ссылку
            <div class="reward-badge">?</div>
        </button>
        
        <button id="watchVideoButton" class="test-button">
            Смотреть видео
            <div class="reward-badge">?</div>
        </button>
        
        <button id="openAdButton" class="test-button">
            Кликнуть по баннеру
            <div class="reward-badge">?</div>
        </button>
    </div>
    
    <div>
        <h2>Управление:</h2>
        <button onclick="testLoadRewards()" class="test-button">Загрузить награды</button>
        <button onclick="testUpdateBadges()" class="test-button">Обновить badges</button>
        <button onclick="testGetRewards()" class="test-button">Показать награды</button>
        <button onclick="clearLog()" class="test-button">Очистить лог</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        // Простая система логирования
        function log(message) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logEl.innerHTML += `[${time}] ${message}<br>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Загружаем reward-badges-manager.js
        const script = document.createElement('script');
        script.src = 'js/reward-badges-manager.js';
        script.onload = () => {
            log('✅ RewardBadgesManager загружен');
            setTimeout(() => {
                testLoadRewards();
            }, 500);
        };
        script.onerror = () => {
            log('❌ Ошибка загрузки RewardBadgesManager');
        };
        document.head.appendChild(script);
        
        // Тестовые функции
        async function testLoadRewards() {
            log('🔄 Тестируем загрузку наград...');
            
            if (!window.rewardBadgesManager) {
                log('❌ RewardBadgesManager не найден');
                return;
            }
            
            try {
                await window.rewardBadgesManager.init();
                log('✅ Награды загружены успешно');
                
                const rewards = window.rewardBadgesManager.getAllRewards();
                log(`📊 Награды: ${JSON.stringify(rewards)}`);
                
            } catch (error) {
                log(`❌ Ошибка загрузки наград: ${error.message}`);
            }
        }
        
        function testUpdateBadges() {
            log('🔄 Тестируем обновление badges...');
            
            if (!window.rewardBadgesManager) {
                log('❌ RewardBadgesManager не найден');
                return;
            }
            
            window.rewardBadgesManager.updateAllBadges();
            log('✅ Badges обновлены');
        }
        
        function testGetRewards() {
            if (!window.rewardBadgesManager) {
                log('❌ RewardBadgesManager не найден');
                return;
            }
            
            const rewards = window.rewardBadgesManager.getAllRewards();
            log(`📊 Текущие награды: ${JSON.stringify(rewards)}`);
        }
        
        log('🚀 Тестовая страница загружена');
    </script>
</body>
</html>
