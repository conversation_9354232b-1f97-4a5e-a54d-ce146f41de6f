// === ads-manager-full.js ===
// ПОЛНЫЙ АВТОНОМНЫЙ МОДУЛЬ РЕКЛАМЫ - ВСЯ ЛОГИКА ИЗ ОРИГИНАЛА
// Содержит ВСЕ функции для работы с RichAds без зависимостей

console.log('🚀 [AdsManagerFull] Загрузка ПОЛНОГО автономного модуля рекламы...');

// ===== КОНСТАНТЫ ИЗ ОРИГИНАЛА =====
const MY_PUB_ID = "944840";
const MY_APP_ID = "2122";
const DEBUG_MODE = false;

// Типы рекламы из оригинала
window.AD_TYPES = {
  NATIVE_BANNER: 'native_banner',
  REWARDED_VIDEO: 'rewarded_video',
  INTERSTITIAL: 'interstitial'
};

class AdsManagerFull {
  constructor() {
    console.log('🏗️ [AdsManagerFull] Создание ПОЛНОГО автономного экземпляра...');

    // ===== ОСНОВНЫЕ СВОЙСТВА ИЗ ОРИГИНАЛА =====
    this.adsController = null;
    this.isButtonPressed = false;
    this.isInitialized = false;
    this.isAdShowing = false;
    this.lastAdShownTime = 0;
    this.adCooldownTime = 20000; // 20 секунд как в оригинале
    this.countdownTimer = null;
    this.currentButton = null;

    // Попытки инициализации
    this.initializationAttempts = 0;
    this.maxInitializationAttempts = 50;

    // Глобальная блокировка кликов
    this.globalClickBlocked = false;

    // Элементы интерфейса (для совместимости)
    this.elements = {};

    // Система лимитов рекламы (из централизованной конфигурации)
    this.dailyLimits = {};
    this.initializeFromConfig();

    console.log('✅ [AdsManagerFull] Автономный экземпляр создан');

    // ===== ЭКСПОРТ ГЛОБАЛЬНЫХ ПЕРЕМЕННЫХ ДЛЯ СОВМЕСТИМОСТИ =====
    window.adsController = this.adsController;
    window.isButtonPressed = this.isButtonPressed;
    window.isAdShowing = this.isAdShowing;
    window.adsBlocked = false;
  }

  /**
   * Инициализация конфигурации из AdsConfig
   */
  initializeFromConfig() {
    if (window.AdsConfig) {
      // Получаем лимиты из централизованной конфигурации
      const dailyLimit = window.AdsConfig.LIMITS?.DAILY_LIMIT_PER_TYPE || 20;

      // Получаем типы рекламы и создаем лимиты
      Object.values(window.AdsConfig.AD_TYPES).forEach(adType => {
        this.dailyLimits[adType.id] = dailyLimit;
      });

      console.log('[AdsManagerFull] ✅ Конфигурация загружена из AdsConfig:', this.dailyLimits);
    } else {
      // Fallback на старые значения
      this.dailyLimits = {
        'native_banner': 20,
        'rewarded_video': 20,
        'interstitial': 20
      };

      console.warn('[AdsManagerFull] ⚠️ AdsConfig не найден, используем fallback конфигурацию');
    }
  }

  // ===== ГЛАВНЫЙ МЕТОД ИНИЦИАЛИЗАЦИИ =====
  init() {
    console.log('[AdsManagerFull] 🚀 ВЫЗВАН МЕТОД INIT()');

    if (this.isInitialized) {
      console.log('[AdsManagerFull] Уже инициализирован');
      return;
    }

    console.log('[AdsManagerFull] 🚀 НАЧИНАЕМ АВТОНОМНУЮ ИНИЦИАЛИЗАЦИЮ...');

    // 1. Настраиваем обработчики кнопок
    this.setupEventListeners();

    // 2. Инициализируем RichAds SDK
    this.initializeRichAds();

    // 3. Настраиваем глобальную защиту от кликов
    this.setupGlobalClickProtection();

    this.isInitialized = true;

    // Обновляем отображение лимитов при инициализации
    setTimeout(() => {
      this.updateLimitsDisplay();

      // Инициализируем серверную систему счетчиков
      if (window.serverAdCountersManager) {
        window.serverAdCountersManager.init();
      } else if (window.adCountersManager) {
        // Fallback: локальная система
        window.adCountersManager.init();
      }
    }, 100);

    console.log('[AdsManagerFull] ✅ АВТОНОМНАЯ ИНИЦИАЛИЗАЦИЯ ЗАВЕРШЕНА');
  }

  // ===== НАСТРОЙКА ОБРАБОТЧИКОВ КНОПОК =====
  setupEventListeners() {
    console.log('[AdsManagerFull] 🔧 Настройка обработчиков кнопок...');

    // Защита от повторной инициализации
    if (this.listenersSetup) {
      console.log('[AdsManagerFull] ⚠️ Обработчики уже настроены, пропускаем');
      return;
    }

    // Находим все кнопки рекламы
    const buttons = {
      openLinkButton: document.getElementById('openLinkButton'),
      watchVideoButton: document.getElementById('watchVideoButton'),
      openAdButton: document.getElementById('openAdButton')
    };

    console.log('[AdsManagerFull] Найденные кнопки:', buttons);

    // Назначаем обработчики с проверками
    if (buttons.openLinkButton) {
      buttons.openLinkButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('[AdsManagerFull] 🔗 КЛИК: openLinkButton (автопереход баннер)');
        this.handleNativeBannerClick();
      });
      console.log('[AdsManagerFull] ✅ openLinkButton подключен → NATIVE_BANNER');
    }

    if (buttons.watchVideoButton) {
      buttons.watchVideoButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('[AdsManagerFull] 📺 КЛИК: watchVideoButton (видео реклама)');
        this.handleVideoAdClick();
      });
      console.log('[AdsManagerFull] ✅ watchVideoButton подключен → REWARDED_VIDEO');
    }

    if (buttons.openAdButton) {
      buttons.openAdButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('[AdsManagerFull] 💰 КЛИК: openAdButton (интерстишиал баннер)');
        this.handleInterstitialBannerClick();
      });
      console.log('[AdsManagerFull] ✅ openAdButton подключен → INTERSTITIAL');
    }

    console.log('[AdsManagerFull] ✅ Все обработчики кнопок настроены');
    this.listenersSetup = true;
  }

  // ===== ОЖИДАНИЕ И СОЗДАНИЕ RICHADDS =====
  waitForRichAdsSDK() {
    console.log('[AdsManagerFull] ⏳ Ожидание RichAds SDK...');

    if (window.appUtils) {
      const loadingText = window.appLocalization ? window.appLocalization.get('ads.loading_ad_module') : "Загрузка модуля рекламы...";
      window.appUtils.showStatus(loadingText, "info");
    }

    const checkInterval = setInterval(() => {
      this.initializationAttempts++;
      console.log(`[AdsManagerFull] Попытка ${this.initializationAttempts}: проверка SDK...`);

      if (typeof TelegramAdsController === "function") {
        clearInterval(checkInterval);
        console.log('[AdsManagerFull] ✅ RichAds SDK найден!');
        this.createRichAdsController();
      } else if (this.initializationAttempts >= this.maxInitializationAttempts) {
        clearInterval(checkInterval);
        console.error('[AdsManagerFull] ❌ ТАЙМ-АУТ ЗАГРУЗКИ SDK');
        this.handleRichAdsError('Не удалось загрузить модуль рекламы');
      }
    }, 100);
  }

  createRichAdsController() {
    try {
      console.log('[AdsManagerFull] 🔧 Создание RichAds контроллера...');

      // ПРОВЕРКА: Если не в Telegram WebApp - блокируем рекламу
      if (!window.Telegram?.WebApp) {
        console.error('[AdsManagerFull] ❌ НЕ В TELEGRAM WEBAPP - реклама недоступна');
        this.adsController = null;
        this.disableAllAdButtons();
        if (window.appUtils) {
          window.appUtils.showStatus('Реклама доступна только в Telegram', 'error');
        }
        return;
      }

      // Создаем контроллер
      this.adsController = new TelegramAdsController();

      // Инициализируем с правильными параметрами
      console.log('[AdsManagerFull] 🔧 Инициализация с параметрами:', {
        pubId: MY_PUB_ID,
        appId: MY_APP_ID,
        debug: DEBUG_MODE
      });

      this.adsController.initialize({
        pubId: MY_PUB_ID,
        appId: MY_APP_ID,
        debug: DEBUG_MODE
      });

      // Экспортируем глобально
      window.adsController = this.adsController;

      console.log('[AdsManagerFull] ✅ RichAds контроллер создан и инициализирован');

      // ЧЕТКОЕ УВЕДОМЛЕНИЕ ОБ УСПЕХЕ
      if (window.appUtils) {
        const readyText = window.appLocalization ?
          window.appLocalization.get('ads.ads_ready') :
          'Реклама готова!';
        window.appUtils.showStatus(readyText, "success");
      }

    } catch (error) {
      console.error('[AdsManagerFull] ❌ Ошибка создания RichAds:', error);
      this.handleRichAdsError(`Ошибка создания контроллера: ${error.message}`);
    }
  }

  handleRichAdsError(errorMessage) {
    console.error('[AdsManagerFull] ❌ ОШИБКА RICHADDS:', errorMessage);

    // Показываем ошибку и блокируем кнопки
    if (window.appUtils) {
      const errorText = window.appLocalization ?
        window.appLocalization.get('ads.module_not_loaded') :
        'Модуль рекламы не загружен. Попробуйте перезапустить приложение.';
      window.appUtils.showStatus(errorText, "error");
    }

    console.error('[AdsManagerFull] ❌ Реклама недоступна - блокируем кнопки');

    // Блокируем все рекламные кнопки
    this.disableAllAdButtons('Реклама недоступна');

    // Устанавливаем null контроллер
    this.adsController = null;
    window.adsController = null;

    console.log('[AdsManagerFull] ❌ Рекламные кнопки заблокированы');
  }



  // ===== ЗАЩИТА ТОЛЬКО РЕКЛАМНЫХ КНОПОК =====
  setupGlobalClickProtection() {
    console.log('[AdsManagerFull] 🛡️ Настройка защиты ТОЛЬКО рекламных кнопок...');

    // Перехватываем клики только на рекламных кнопках
    document.addEventListener('click', (event) => {
      // Проверяем, является ли цель клика рекламной кнопкой
      const target = event.target.closest('#openLinkButton, #watchVideoButton, #openAdButton');

      if (target && (this.globalClickBlocked || window.adsBlocked || this.isButtonPressed)) {
        console.warn(`[AdsManagerFull] 🚫 БЛОКИРОВКА РЕКЛАМНОЙ КНОПКИ: ${target.id} заблокирована`);
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        return false;
      }

      // Все остальные кнопки (навигация, формы и т.д.) работают нормально
    }, true);

    console.log('[AdsManagerFull] ✅ Защита настроена ТОЛЬКО для рекламных кнопок');
  }

  /**
   * Инициализирует RichAds SDK (из оригинала)
   */
  initializeRichAds() {
    if (window.appUtils) {
      const initText = window.appLocalization ? window.appLocalization.get('status.initializing_ads') : "Инициализация рекламы...";
      window.appUtils.showStatus(initText);
    }

    try {
      console.log("[AdsManager] Проверка доступности SDK...");
      console.log("[AdsManager] Telegram WebApp:", !!window.Telegram?.WebApp);
      console.log("[AdsManager] TelegramAdsController:", typeof TelegramAdsController);

      // ИСПРАВЛЕНИЕ: Проверяем что мы в Telegram WebApp
      if (!window.Telegram?.WebApp) {
        console.error("[AdsManager] Не в Telegram WebApp - реклама недоступна");
        if (window.appUtils) {
          window.appUtils.showStatus("Реклама недоступна (не в Telegram)", "error");
        }
        // Блокируем кнопки - реклама недоступна
        this.disableAllAdButtons('Реклама недоступна');
        this.adsController = null;
        this.isInitialized = true;
        return;
      }

      // Проверяем наличие SDK
      if (typeof TelegramAdsController !== "function" && typeof TelegramAdsController !== "object") {
        console.warn("[AdsManager] SDK не найден, пробуем загрузить скрипт вручную");

        // ИСПРАВЛЕНИЕ: Загружаем скрипт с правильными параметрами
        const script = document.createElement('script');
        script.src = "https://richinfo.co/richpartners/telegram/js/tg-ob.js";
        script.async = true;
        script.onload = () => {
          console.log("[AdsManager] Скрипт RichAds загружен");
          setTimeout(() => {
            this.initializeRichAdsAfterLoad();
          }, 500);
        };
        script.onerror = () => {
          console.error("[AdsManager] ❌ Ошибка загрузки скрипта RichAds - реклама недоступна");
          if (window.appUtils) {
            const loadErrorText = window.appLocalization ? window.appLocalization.get('ads.load_error') : "Ошибка загрузки модуля рекламы";
            window.appUtils.showStatus(loadErrorText, "error");
          }
          // БЕЗОПАСНОСТЬ: НЕ создаем эмуляцию - блокируем рекламу полностью
          this.adsController = null;
          this.disableAllAdButtons();
        };
        document.head.appendChild(script);

        return;
      }

      // Если SDK доступен, продолжаем инициализацию
      this.initializeRichAdsAfterLoad();

    } catch (error) {
      console.error("[AdsManager] КРИТИЧЕСКАЯ ОШИБКА:", error);
      if (window.appUtils) {
        const errorText = window.appLocalization ?
          window.appLocalization.get('ads.initialization_error') :
          'Ошибка инициализации рекламы';
        window.appUtils.showStatus(`${errorText}: ${error.message}`, "error");
      }
      if (window.Telegram?.WebApp) {
        window.Telegram.WebApp.showAlert(`Ошибка модуля рекламы:\n${error.message}`);
      }

      // БЕЗОПАСНОСТЬ: НЕ создаем эмуляцию - блокируем рекламу полностью
      this.adsController = null;
      this.disableAllAdButtons();
    }
  }

  /**
   * Инициализирует RichAds SDK после загрузки скрипта (из оригинала)
   */
  initializeRichAdsAfterLoad() {
    try {
      console.log("[AdsManager] Попытка создания экземпляра SDK...");

      // ИСПРАВЛЕНИЕ: Проверяем, доступен ли SDK как функция (из оригинала)
      if (typeof TelegramAdsController === "function") {
        console.log("[AdsManager] SDK доступен как функция, создаем экземпляр");

        // ИСПРАВЛЕНИЕ: Создаем контроллер как в оригинале - БЕЗ параметров в конструкторе
        this.adsController = new TelegramAdsController();

        // 🚀 ПРАВИЛЬНАЯ инициализация с константами из оригинала
        const MY_PUB_ID = "944840";
        const MY_APP_ID = "2122";
        const DEBUG_MODE = false;

        console.log("[AdsManager] Вызов метода initialize с параметрами:", {
          pubId: MY_PUB_ID,
          appId: MY_APP_ID,
          debug: DEBUG_MODE
        });

        this.adsController.initialize({
          pubId: MY_PUB_ID,
          appId: MY_APP_ID,
          debug: DEBUG_MODE,
        });

        window.adsController = this.adsController;

        // ИСПРАВЛЕНИЕ: Включаем кнопки после инициализации
        setTimeout(() => {
          console.log("[AdsManager] RichAds SDK инициализирован");
          if (window.appUtils) {
            window.appUtils.showStatus("Реклама готова!", "success");
          }
          this.enableAllAdButtons();
        }, 1000);
      }
      // Проверяем, доступен ли SDK как объект
      else if (typeof TelegramAdsController === "object" && TelegramAdsController !== null) {
        console.log("[AdsManager] SDK доступен как объект, используем его");
        this.adsController = TelegramAdsController;
        window.adsController = this.adsController;

        // Включаем кнопки если SDK уже готов
        this.enableAllAdButtons();
      }
      else {
        throw new Error("TelegramAdsController недоступен после загрузки скрипта");
      }

      console.log("[AdsManager] ✅ RichAds SDK успешно инициализирован");

    } catch (error) {
      console.error("[AdsManager] Ошибка инициализации SDK:", error);
      if (window.appUtils) {
        const sdkErrorText = window.appLocalization ?
          window.appLocalization.get('ads.sdk_error').replace('{error}', error.message) :
          `Ошибка SDK: ${error.message}`;
        window.appUtils.showStatus(sdkErrorText, "error");
      }
      // НЕ блокируем кнопки при ошибке SDK - разрешаем эмуляцию
      console.log("[AdsManager] Ошибка SDK, но кнопки остаются активными для эмуляции");
    }
  }

  /**
   * 🔒 БЛОКИРОВКА только рекламных кнопок (НЕ БЛОКИРУЕМ НАВИГАЦИЮ!)
   */
  disableAllAdButtons() {
    console.log('[AdsManager] 🔒 Блокировка ТОЛЬКО рекламных кнопок (навигация остается активной)');

    // 🛡️ ВКЛЮЧАЕМ ЗАЩИТУ ТОЛЬКО ДЛЯ РЕКЛАМЫ (флаги для совместимости)
    this.globalClickBlocked = true;
    window.adsBlocked = true;

    // Блокируем ТОЛЬКО конкретные рекламные кнопки (НЕ ВСЕ ПОДРЯД!)
    const adButtonIds = ['openLinkButton', 'watchVideoButton', 'openAdButton'];

    adButtonIds.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        this.blockButton(button);
        console.log(`[AdsManager] 🔒 Заблокирована рекламная кнопка: ${buttonId}`);
      }
    });

    console.log('[AdsManager] ✅ Заблокированы ТОЛЬКО рекламные кнопки, НАВИГАЦИЯ РАБОТАЕТ!');
  }

  /**
   * 🔒 Блокирует конкретную кнопку АГРЕССИВНО (БЕЗ удаления обработчиков)
   */
  blockButton(button) {
    if (!button) return;

    button.disabled = true;
    button.style.pointerEvents = 'none !important';
    button.style.opacity = '0.3';
    button.style.cursor = 'not-allowed !important';
    button.style.userSelect = 'none';
    button.setAttribute('data-blocked', 'true');

    // НЕ удаляем обработчики событий - просто блокируем визуально
    console.log(`[AdsManager] 🔒 Кнопка ${button.id || button.className} заблокирована (обработчики сохранены)`);
  }

  /**
   * 🔓 ВОССТАНАВЛИВАЕТ только рекламные кнопки
   */
  enableAllAdButtons() {
    console.log('[AdsManager] 🔓 Восстанавливаем ТОЛЬКО рекламные кнопки');

    // 🛡️ ОТКЛЮЧАЕМ ЗАЩИТУ РЕКЛАМЫ (флаги для совместимости)
    this.globalClickBlocked = false;
    window.adsBlocked = false;

    // Восстанавливаем ТОЛЬКО конкретные рекламные кнопки
    const adButtonIds = ['openLinkButton', 'watchVideoButton', 'openAdButton'];

    adButtonIds.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        this.unblockButton(button);
        console.log(`[AdsManager] 🔓 Восстановлена рекламная кнопка: ${buttonId}`);
      }
    });

    console.log('[AdsManager] ✅ Восстановлены ТОЛЬКО рекламные кнопки, НАВИГАЦИЯ НЕ ЗАТРОНУТА');
  }

  /**
   * 🔓 Восстанавливает конкретную кнопку
   */
  unblockButton(button) {
    if (!button) return;

    button.disabled = false;
    button.style.pointerEvents = 'auto';
    button.style.opacity = '1';
    button.style.cursor = 'pointer';
    button.style.userSelect = 'auto';
    button.removeAttribute('data-blocked');

    console.log(`[AdsManager] 🔓 Кнопка ${button.id || button.className} восстановлена`);
  }

  // ===== СИСТЕМА ЛИМИТОВ РЕКЛАМЫ =====

  /**
   * Получает текущую дату в формате YYYY-MM-DD по UTC
   */
  getCurrentUTCDate() {
    const now = new Date();
    return now.getUTCFullYear() + '-' +
           String(now.getUTCMonth() + 1).padStart(2, '0') + '-' +
           String(now.getUTCDate()).padStart(2, '0');
  }

  /**
   * Получает ключ для localStorage для конкретного типа рекламы
   */
  getAdCountKey(adType) {
    const today = this.getCurrentUTCDate();
    return `ad_count_${adType}_${today}`;
  }

  /**
   * Получает количество просмотренной рекламы определенного типа за сегодня
   */
  getTodayAdCount(adType) {
    const key = this.getAdCountKey(adType);
    const count = localStorage.getItem(key);
    return count ? parseInt(count, 10) : 0;
  }

  /**
   * Увеличивает счетчик просмотров рекламы определенного типа
   */
  incrementAdCount(adType) {
    const key = this.getAdCountKey(adType);
    const currentCount = this.getTodayAdCount(adType);
    const newCount = currentCount + 1;
    localStorage.setItem(key, newCount.toString());
    console.log(`[AdsManagerFull] 📊 Счетчик ${adType}: ${newCount}/${this.dailyLimits[adType]}`);
    return newCount;
  }

  /**
   * Проверяет, достигнут ли лимит для определенного типа рекламы
   */
  isAdLimitReached(adType) {
    const currentCount = this.getTodayAdCount(adType);
    const limit = this.dailyLimits[adType] || 20;
    return currentCount >= limit;
  }

  /**
   * Получает время до следующего дня по UTC
   */
  getTimeUntilNextDay() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
    tomorrow.setUTCHours(0, 0, 0, 0);

    const msUntilTomorrow = tomorrow.getTime() - now.getTime();
    const hoursUntil = Math.floor(msUntilTomorrow / (1000 * 60 * 60));
    const minutesUntil = Math.floor((msUntilTomorrow % (1000 * 60 * 60)) / (1000 * 60));

    return {
      hours: hoursUntil,
      minutes: minutesUntil,
      date: tomorrow.getUTCDate(),
      month: tomorrow.getUTCMonth() + 1
    };
  }

  /**
   * Показывает сообщение о достижении лимита с информацией о всех типах рекламы
   */
  showLimitReachedMessage(adType) {
    const adTypeNames = {
      'native_banner': 'автопереходов по баннеру',
      'rewarded_video': 'видеорекламы',
      'interstitial': 'интерстициальных баннеров'
    };

    const timeUntil = this.getTimeUntilNextDay();
    const adTypeName = adTypeNames[adType] || 'рекламы';

    // Получаем информацию о всех лимитах
    const allLimits = this.getLimitsInfo();
    const limitsText = Object.keys(allLimits).map(type => {
      const data = allLimits[type];
      const typeName = adTypeNames[type];
      return `• ${typeName}: ${data.remaining} из ${data.limit}`;
    }).join('\n');

    const message = `Дневной лимит ${adTypeName} достигнут (${this.dailyLimits[adType]} показов).\n\nОсталось успешных показов на сегодня:\n${limitsText}\n\nВсе лимиты сбросятся:\n${timeUntil.date}.${String(timeUntil.month).padStart(2, '0')} в 00:00 UTC\n(через ${timeUntil.hours}ч ${timeUntil.minutes}мин)`;

    console.log(`[AdsManagerFull] 🚫 Лимит достигнут для ${adType}: ${message}`);

    if (window.appUtils) {
      window.appUtils.showStatus(`Лимит ${adTypeName} достигнут на сегодня`, "warning");
    }

    if (window.Telegram?.WebApp?.showAlert) {
      window.Telegram.WebApp.showAlert(message);
    } else {
      alert(message);
    }
  }

  /**
   * Получает информацию о текущих лимитах для отображения в интерфейсе
   */
  getLimitsInfo() {
    const info = {};
    const adTypes = ['native_banner', 'rewarded_video', 'interstitial'];

    adTypes.forEach(adType => {
      const current = this.getTodayAdCount(adType);
      const limit = this.dailyLimits[adType];
      const remaining = Math.max(0, limit - current);

      info[adType] = {
        current,
        limit,
        remaining,
        isLimitReached: current >= limit
      };
    });

    return info;
  }

  /**
   * Блокирует конкретную кнопку при достижении лимита
   */
  updateLimitsDisplay() {
    let info;

    // Пробуем получить данные из серверной системы
    if (window.serverAdCountersManager && window.serverAdCountersManager.isInitialized) {
      info = window.serverAdCountersManager.getAllLimitsInfo();
    } else {
      // Fallback: используем локальную систему
      info = this.getLimitsInfo();
    }

    // Блокируем/разблокируем кнопки в зависимости от лимитов
    Object.keys(info).forEach(adType => {
      const data = info[adType];
      const buttonIds = {
        'native_banner': 'openLinkButton',
        'rewarded_video': 'watchVideoButton',
        'interstitial': 'openAdButton'
      };

      const button = document.getElementById(buttonIds[adType]);
      if (button) {
        if (data.isLimitReached) {
          // Блокируем кнопку при достижении лимита
          button.disabled = true;
          button.style.opacity = '0.5';
          button.style.cursor = 'not-allowed';
          button.title = 'Дневной лимит достигнут';
          button.setAttribute('data-limit-reached', 'true');
        } else {
          // Разблокируем кнопку если лимит не достигнут
          button.disabled = false;
          button.style.opacity = '1';
          button.style.cursor = 'pointer';
          button.title = '';
          button.removeAttribute('data-limit-reached');
        }
      }
    });

    console.log('[AdsManagerFull] 📊 Лимиты обновлены:', info);
  }

  // ===== ЛОГИРОВАНИЕ КЛИКОВ =====
  /**
   * Определяет тип рекламы по кнопке
   */
  getAdTypeFromButton(button) {
    if (!button || !button.id) return 'unknown';

    switch (button.id) {
      case 'openLinkButton':
        return 'native_banner';
      case 'watchVideoButton':
        return 'rewarded_video';
      case 'openAdButton':
        return 'interstitial';
      default:
        return 'unknown';
    }
  }

  /**
   * Извлекает userId из Telegram initData
   */
  extractUserIdFromInitData(initData) {
    try {
      // Парсим initData для извлечения userId
      const params = new URLSearchParams(initData);
      const userParam = params.get('user');

      if (userParam) {
        const userData = JSON.parse(decodeURIComponent(userParam));
        return userData.id;
      }

      // БЕЗОПАСНОСТЬ: Никаких fallback для тестирования

      return null;
    } catch (error) {
      console.error('[AdsManager] Ошибка извлечения userId:', error);
      return null;
    }
  }

  /**
   * Получает текущий userId пользователя
   */
  getCurrentUserId() {
    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      return 12345; // Fallback для тестирования
    }
    return this.extractUserIdFromInitData(initData);
  }

  /**
   * Блокирует все кнопки рекламы при недоступности SDK
   */
  disableAllAdButtons() {
    const buttons = ['openLinkButton', 'watchVideoButton', 'openAdButton'];

    buttons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.disabled = true;
        button.style.opacity = '0.5';
        button.style.cursor = 'not-allowed';
        button.title = 'Реклама недоступна - RichAds SDK не загружен';

        // Удаляем все обработчики событий
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
      }
    });

    console.log('[AdsManagerFull] 🔒 Все кнопки рекламы заблокированы');
  }

  /**
   * Получает информацию о лимитах для логирования
   */
  getLimitsInfo() {
    const limits = {};

    // Получаем информацию о лимитах из localStorage
    const adLimits = JSON.parse(localStorage.getItem('adLimits') || '{}');

    for (const [type, data] of Object.entries(adLimits)) {
      limits[type] = {
        current: data.count || 0,
        max: data.limit || 20,
        remaining: Math.max(0, (data.limit || 20) - (data.count || 0))
      };
    }

    return limits;
  }

  /**
   * Логирует клик по рекламной кнопке
   */
  async logAdClick(adType, clickType, reason = '') {
    try {
      let initData = window.Telegram?.WebApp?.initData;

      // Fallback для локальной разработки
      if (!initData) {
        console.warn('[AdsManagerFull] Нет initData, используем тестовые данные для разработки');
        // Правильно кодируем тестовые данные
        const testUser = JSON.stringify({id: 12345, first_name: "Test", username: "testuser"});
        initData = `user=${encodeURIComponent(testUser)}&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test`;
      }

      const logData = {
        initData: initData,
        adType: adType,
        clickType: clickType,
        timestamp: Date.now(),
        sessionId: window.sessionStorage?.getItem('session_id') || 'unknown'
      };

      if (reason) {
        logData.reason = reason;
      }

      // Добавляем информацию о лимитах
      if (clickType === 'limit_reached') {
        logData.limitInfo = this.getLimitsInfo();
      }

      const response = await fetch(`${window.API_BASE_URL}/logAdClick.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(logData)
      });

      if (!response.ok) {
        console.warn('[AdsManagerFull] Ошибка логирования клика:', response.status);
      }
    } catch (error) {
      console.warn('[AdsManagerFull] Ошибка при логировании клика:', error);
    }
  }

  // ===== ОБРАБОТЧИКИ КЛИКОВ =====
  // НОВЫЕ ПРАВИЛЬНЫЕ МЕТОДЫ
  handleNativeBannerClick() {
    console.log("[AdsManagerFull] 🔗 КЛИК: Автопереход по баннеру (NATIVE_BANNER)");

    // Логируем клик по кнопке
    this.logAdClick('native_banner', 'button_click');

    // КРИТИЧЕСКИ ВАЖНО: Регистрируем ожидающую награду ПЕРЕД показом рекламы
    const userId = this.getCurrentUserId();
    if (userId && window.richAdsSecurityManager) {
      const rewardData = window.richAdsSecurityManager.registerPendingReward(userId, 'native_banner');
      console.log(`[AdsManagerFull] 🔒 Зарегистрирована ожидающая награда: ${rewardData.rewardId}`);
    }

    // Проверяем лимит для автопереходов по баннеру
    if (this.isAdLimitReached('native_banner')) {
      this.logAdClick('native_banner', 'limit_reached', 'Daily limit reached');
      this.showLimitReachedMessage('native_banner');
      return;
    }

    if (this.isAdShowing) {
      console.warn("[AdsManagerFull] ⚠️ Реклама уже показывается");
      this.logAdClick('native_banner', 'ad_already_showing', 'Another ad is currently showing');
      if (window.appUtils) {
        const waitText = window.appLocalization ? window.appLocalization.get('ads.wait_ad_showing') : "Подождите, реклама уже показывается...";
        window.appUtils.showStatus(waitText, "warning");
      }
      return;
    }

    if (!this.adsController) {
      console.error("[AdsManagerFull] ❌ Реклама недоступна");
      this.logAdClick('native_banner', 'ads_unavailable', 'Ads controller not initialized');
      if (window.appUtils) {
        window.appUtils.showStatus("Реклама недоступна", "error");
      }
      if (window.Telegram?.WebApp?.showAlert) {
        const alertText = window.appLocalization ? window.appLocalization.get('ads.module_not_loaded') : "Модуль рекламы не загружен. Попробуйте перезапустить приложение.";
        window.Telegram.WebApp.showAlert(alertText);
      }
      return;
    }

    const button = document.getElementById('openLinkButton');
    this.showAdWithType(window.AD_TYPES.NATIVE_BANNER, button); // АВТОПЕРЕХОД ПО БАННЕРУ
  }

  handleVideoAdClick() {
    console.log("[AdsManagerFull] 📺 КЛИК: Смотреть видеорекламу (REWARDED_VIDEO)");

    // Логируем клик по кнопке
    this.logAdClick('rewarded_video', 'button_click');

    // Проверяем лимит для видеорекламы
    if (this.isAdLimitReached('rewarded_video')) {
      this.logAdClick('rewarded_video', 'limit_reached', 'Daily limit reached');
      this.showLimitReachedMessage('rewarded_video');
      return;
    }

    if (this.isAdShowing) {
      console.warn("[AdsManagerFull] ⚠️ Реклама уже показывается");
      this.logAdClick('rewarded_video', 'ad_already_showing', 'Another ad is currently showing');
      if (window.appUtils) {
        const waitText = window.appLocalization ? window.appLocalization.get('ads.wait_ad_showing') : "Подождите, реклама уже показывается...";
        window.appUtils.showStatus(waitText, "warning");
      }
      return;
    }

    if (!this.adsController) {
      console.error("[AdsManagerFull] ❌ Реклама недоступна");
      this.logAdClick('rewarded_video', 'ads_unavailable', 'Ads controller not initialized');
      if (window.appUtils) {
        const unavailableText = window.appLocalization ? window.appLocalization.get('ads.ads_unavailable') : "Реклама недоступна";
        window.appUtils.showStatus(unavailableText, "error");
      }
      if (window.Telegram?.WebApp?.showAlert) {
        const alertText = window.appLocalization ? window.appLocalization.get('ads.module_not_loaded') : "Модуль рекламы не загружен. Попробуйте перезапустить приложение.";
        window.Telegram.WebApp.showAlert(alertText);
      }
      return;
    }

    const button = document.getElementById('watchVideoButton');
    this.showAdWithType(window.AD_TYPES.REWARDED_VIDEO, button); // ИСКЛЮЧИТЕЛЬНО ВИДЕОРЕКЛАМА
  }

  handleInterstitialBannerClick() {
    console.log("[AdsManagerFull] 💰 КЛИК: Интерстициальный баннер (INTERSTITIAL)");

    // Логируем клик по кнопке
    this.logAdClick('interstitial', 'button_click');

    // Проверяем лимит для интерстициальных баннеров
    if (this.isAdLimitReached('interstitial')) {
      this.logAdClick('interstitial', 'limit_reached', 'Daily limit reached');
      this.showLimitReachedMessage('interstitial');
      return;
    }

    if (this.isAdShowing) {
      console.warn("[AdsManagerFull] ⚠️ Реклама уже показывается");
      this.logAdClick('interstitial', 'ad_already_showing', 'Another ad is currently showing');
      if (window.appUtils) {
        const waitText = window.appLocalization ? window.appLocalization.get('ads.wait_ad_showing') : "Подождите, реклама уже показывается...";
        window.appUtils.showStatus(waitText, "warning");
      }
      return;
    }

    if (!this.adsController) {
      console.error("[AdsManagerFull] ❌ Реклама недоступна");
      this.logAdClick('interstitial', 'ads_unavailable', 'Ads controller not initialized');
      if (window.appUtils) {
        const unavailableText = window.appLocalization ? window.appLocalization.get('ads.ads_unavailable') : "Реклама недоступна";
        window.appUtils.showStatus(unavailableText, "error");
      }
      if (window.Telegram?.WebApp?.showAlert) {
        const alertText = window.appLocalization ? window.appLocalization.get('ads.module_not_loaded') : "Модуль рекламы не загружен. Попробуйте перезапустить приложение.";
        window.Telegram.WebApp.showAlert(alertText);
      }
      return;
    }

    const button = document.getElementById('openAdButton');
    this.showAdWithType(window.AD_TYPES.INTERSTITIAL, button); // ИНТЕРСТИЦИАЛЬНЫЙ БАННЕР
  }

  // СТАРЫЕ МЕТОДЫ ДЛЯ СОВМЕСТИМОСТИ (алиасы)
  handleWatchAdClick() {
    console.log("[AdsManagerFull] ⚠️ УСТАРЕВШИЙ МЕТОД: handleWatchAdClick → перенаправляем на handleNativeBannerClick");
    this.handleNativeBannerClick();
  }

  handleWatchVideoClick() {
    console.log("[AdsManagerFull] ⚠️ УСТАРЕВШИЙ МЕТОД: handleWatchVideoClick → перенаправляем на handleVideoAdClick");
    this.handleVideoAdClick();
  }

  handleOpenLinkClick() {
    console.log("[AdsManagerFull] ⚠️ УСТАРЕВШИЙ МЕТОД: handleOpenLinkClick → перенаправляем на handleInterstitialBannerClick");
    this.handleInterstitialBannerClick();
  }

  /**
   * Показывает рекламу определенного типа (из оригинала)
   */
  async showAdWithType(adType, button) {
    console.log(`[AdsManager] Показ рекламы типа: ${adType}`);

    // Логируем запрос на показ рекламы
    this.logAdClick(adType, 'ad_request', 'User requested ad show');

    // ИСПРАВЛЕНИЕ: Сохраняем текущую кнопку для обработчиков событий (из оригинала)
    this.currentButton = button;

    // ИСПРАВЛЕНИЕ: Проверка на кулдаун между показами рекламы (из оригинала)
    const currentTime = Date.now();
    if (this.isAdShowing) {
      console.warn("[AdsManager] Реклама уже показывается, игнорируем клик");
      if (window.appUtils) {
        const waitText = window.appLocalization ? window.appLocalization.get('ads.wait_ad_showing') : "Подождите, реклама уже показывается...";
        window.appUtils.showStatus(waitText, "info");
      }
      return;
    }

    if (currentTime - this.lastAdShownTime < this.adCooldownTime) {
      console.warn("[AdsManager] Слишком частые запросы рекламы, игнорируем клик");
      if (window.appUtils) {
        const cooldownText = window.appLocalization ? window.appLocalization.get('ads.wait_before_next') : "Подождите немного перед следующим просмотром...";
        window.appUtils.showStatus(cooldownText, "info");
      }
      return;
    }

    // ИСПРАВЛЕНИЕ: Устанавливаем флаг показа рекламы (из оригинала)
    this.isAdShowing = true;
    this.lastAdShownTime = currentTime;

    // ИСПРАВЛЕНИЕ: Показываем правильный статус запроса рекламы с переводами
    const statusMessages = {
      'native_banner': window.appLocalization ? window.appLocalization.get('ads.requesting_banner_redirect') : "Запрос автоперехода по баннеру...",
      'rewarded_video': window.appLocalization ? window.appLocalization.get('ads.requesting_video') : "Запрос видеорекламы...",
      'interstitial': window.appLocalization ? window.appLocalization.get('ads.requesting_interstitial') : "Запрос интерстициального баннера..."
    };

    if (window.appUtils) {
      const defaultMessage = window.appLocalization ? window.appLocalization.get('ads.requesting_ad') : "Запрос рекламы...";
      window.appUtils.showStatus(statusMessages[adType] || defaultMessage, "info");
    }

    // Вибрация при клике (из оригинала)
    if (window.Telegram?.WebApp?.HapticFeedback) {
      window.Telegram.WebApp.HapticFeedback.impactOccurred('light');
    }

    try {
      let adResult;

      // ИСПРАВЛЕНИЕ: Проверяем доступность контроллера и методов (из оригинала)
      if (!this.adsController) {
        throw new Error("Контроллер рекламы не инициализирован");
      }

      // 🚀 ПРАВИЛЬНЫЕ методы RichAds для каждого типа рекламы
      switch (adType) {
        case 'banner':
        case 'native_banner':
          console.log("[AdsManager] 🎬 Запуск автоперехода по баннеру (БЕЗ fallback)...");
          // ТОЛЬКО ОСНОВНОЙ МЕТОД для автоперехода по баннеру, БЕЗ fallback
          console.log("[AdsManager] Вызов triggerInterstitialBanner(true) для АВТОПЕРЕХОДА по баннеру");
          adResult = await this.adsController.triggerInterstitialBanner(true);
          break;

        case 'video':
        case 'rewarded_video':
          console.log("[AdsManager] 📺 Запуск видеорекламы (обычный полноэкранный баннер, БЕЗ fallback)...");
          // Используем обычный полноэкранный баннер метод БЕЗ параметров
          console.log("[AdsManager] Вызов triggerInterstitialBanner() для ВИДЕОРЕКЛАМЫ (полноэкранный баннер)");
          adResult = await this.adsController.triggerInterstitialBanner();
          break;

        case 'interstitial':
        case 'interstitial_banner':
          console.log("[AdsManager] 🔗 Запуск интерстициального баннера (как в первой кнопке, БЕЗ автоперехода, БЕЗ fallback)...");
          // Используем метод как в первой кнопке, но БЕЗ автоперехода - triggerInterstitialBanner(false)
          console.log("[AdsManager] Вызов triggerInterstitialBanner(false) для ИНТЕРСТИЦИАЛЬНОГО БАННЕРА");
          adResult = await this.adsController.triggerInterstitialBanner(false);
          break;

        default:
          console.warn(`[AdsManager] Неизвестный тип рекламы: ${adType}, используем banner`);
          // Используем banner по умолчанию
          if (typeof this.adsController.triggerNativeNotification === 'function') {
            adResult = await this.adsController.triggerNativeNotification(true);
          } else if (typeof this.adsController.triggerInterstitialBanner === 'function') {
            adResult = await this.adsController.triggerInterstitialBanner(true);
          } else {
            throw new Error("Методы рекламы недоступны");
          }
      }

      // ИСПРАВЛЕНИЕ: Обрабатываем успешный результат (из оригинала)
      console.log(`[AdsManager] 🎉 УСПЕШНЫЙ ПРОМИС RICHADDS! ${adType}:`, adResult);

      // ИСПРАВЛЕНИЕ: СРАЗУ после успешного промиса RichAds начисляем награду (как в оригинале)
      const rewardResult = await this.processImmediateReward(adType, button, adResult);

      // ИСПРАВЛЕНИЕ: ПОКАЗЫВАЕМ АЛЕРТ ТОЛЬКО ПОСЛЕ УСПЕШНОГО НАЧИСЛЕНИЯ (КАК В ОРИГИНАЛЕ!)
      if (rewardResult && rewardResult.success) {
        const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        const successMessage = window.appLocalization ?
          window.appLocalization.get('ads.ad_reward_credited') :
          `Награда за просмотр рекламы зачислена! +${rewardResult.reward} ${coinsText}`;

        console.log(`[AdsManager] 🚨 ПОКАЗЫВАЕМ АЛЕРТ ПОСЛЕ УСПЕШНОГО НАЧИСЛЕНИЯ: ${successMessage}`);
        if (window.Telegram?.WebApp?.showPopup) {
          window.Telegram.WebApp.showPopup({
            title: window.appLocalization ? window.appLocalization.get('common.success') : "Успех!",
            message: successMessage,
            buttons: [{
              type: "ok",
              text: window.appLocalization ? window.appLocalization.get('common.excellent') : "Отлично"
            }]
          });
          console.log(`[AdsManager] ✅ Алерт показан через showPopup`);
        } else if (window.Telegram?.WebApp?.showAlert) {
          window.Telegram.WebApp.showAlert(successMessage);
          console.log(`[AdsManager] ✅ Алерт показан через showAlert`);
        } else {
          // Fallback на обычный alert
          alert(successMessage);
          console.log(`[AdsManager] ✅ Алерт показан через обычный alert`);
        }
      }

    } catch (error) {
      this.handleAdError(error, button);
    } finally {
      // ИСПРАВЛЕНИЕ: Сбрасываем флаг показа рекламы (из оригинала)
      this.isAdShowing = false;
      console.log(`[AdsManager] ✅ Показ рекламы завершен`);
    }
  }

  /**
   * Обрабатывает немедленную награду после успешного промиса RichAds (как в оригинале)
   */
  async processImmediateReward(adType, button, adResult = null) {
    console.log(`[AdsManager] 🎉 Промис RichAds успешен! Начисляем награду немедленно: ${adType}`);

    // БЕЗОПАСНОСТЬ: Обрабатываем только реальную рекламу от RichAds

    // ИСПРАВЛЕНИЕ: Получаем данные пользователя из Telegram WebApp (из оригинала)
    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      console.error("[AdsManager] Нет данных пользователя Telegram");
      throw new Error("Нет данных пользователя для начисления награды");
    }

    try {
      // ИСПРАВЛЕНИЕ: Отправляем запрос на сервер для записи просмотра рекламы (из оригинала)
      if (!window.API_BASE_URL) {
        console.error("[AdsManager] ❌ API_BASE_URL не настроен");
        throw new Error("API сервера не настроен");
      }

      const response = await fetch(`${window.API_BASE_URL}/recordAdView.php`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest"
        },
        body: JSON.stringify({
          initData,
          adType,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          // ИСПРАВЛЕНИЕ: Добавляем дополнительные данные для защиты от фрода (из оригинала)
          sessionId: window.sessionStorage.getItem('session_id') || 'unknown',
          viewId: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }),
      });

      if (!response.ok) {
        throw new Error(`Ошибка сервера: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // ИСПРАВЛЕНИЕ: Проверяем ответ сервера (из оригинала)
      if (data.error) {
        console.error("[AdsManager] Ошибка сервера:", data.error);
        throw new Error(data.error);
      }

      if (!data.success) {
        console.warn("[AdsManager] Сервер не подтвердил успешный просмотр");
        throw new Error("Просмотр не засчитан сервером");
      }

      // ИСПРАВЛЕНИЕ: НЕМЕДЛЕННО обрабатываем награду при успешном ответе сервера (КАК В ОРИГИНАЛЕ!)
      if (data.newBalance !== undefined && data.reward > 0) {
        const coinsText3 = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        console.log(`[AdsManager] 🎉 НАГРАДА ПОДТВЕРЖДЕНА СЕРВЕРОМ: +${data.reward} ${coinsText3}`);

        // 📊 ОБНОВЛЯЕМ СЧЕТЧИКИ РЕКЛАМЫ (СЕРВЕРНАЯ СИСТЕМА)
        if (window.serverAdCountersManager && window.serverAdCountersManager.isInitialized) {
          // Используем новую серверную систему
          await window.serverAdCountersManager.incrementCounter(adType);
        } else if (window.adCountersManager) {
          // Fallback: локальная система
          window.adCountersManager.incrementCounter(adType);
        } else {
          // Fallback: старая система
          this.incrementAdCount(adType);
        }

        // 📊 ОБНОВЛЯЕМ ОТОБРАЖЕНИЕ ЛИМИТОВ В ИНТЕРФЕЙСЕ
        this.updateLimitsDisplay();

        // ИСПРАВЛЕНИЕ: 1. СРАЗУ обновляем баланс в интерфейсе (КАК В ОРИГИНАЛЕ - ПЕРВЫМ ДЕЛОМ!)
        if (window.balanceManager) {
          window.balanceManager.updateBalance(data.newBalance, `ad_reward_${adType}`);
          console.log(`[AdsManager] ✅ Баланс обновлен: ${data.newBalance}`);
        }

        // ИСПРАВЛЕНИЕ: 2. СРАЗУ воспроизводим звук монет (КАК В ОРИГИНАЛЕ - ВТОРЫМ ДЕЛОМ!)
        console.log(`[AdsManager] 🎵 Воспроизводим звук для ${data.reward} ${coinsText3}`);
        if (window.audioManager && window.audioManager.playCoinsSound) {
          window.audioManager.playCoinsSound(data.reward);
          console.log(`[AdsManager] ✅ Звук монет воспроизведен через AudioManager`);
        } else if (window.playCoinsSound) {
          window.playCoinsSound(data.reward);
          console.log(`[AdsManager] ✅ Звук монет воспроизведен через глобальную функцию`);
        } else {
          console.warn(`[AdsManager] ⚠️ Звук ${coinsText3} недоступен!`);
        }

        // ИСПРАВЛЕНИЕ: 3. Показываем статус сообщение (КАК В ОРИГИНАЛЕ - ТРЕТЬИМ ДЕЛОМ!)
        if (window.appUtils) {
          const rewardCreditedText = window.appLocalization ?
            window.appLocalization.get('ads.reward_credited_balance').replace('{balance}', data.newBalance) :
            `Награда зачислена! Баланс: ${data.newBalance}`;
          window.appUtils.showStatus(rewardCreditedText, "success");
        }

        // ИСПРАВЛЕНИЕ: 4. Вибрация успеха (КАК В ОРИГИНАЛЕ - ЧЕТВЕРТЫМ ДЕЛОМ!)
        if (window.Telegram?.WebApp?.HapticFeedback) {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('success');
        }

        // ИСПРАВЛЕНИЕ: 5. Автоматически скрываем статус через 2.5 секунды (как в оригинале)
        setTimeout(() => {
          if (window.appUtils?.statusMessageEl?.textContent?.startsWith("Награда зачислена")) {
            window.appUtils.showStatus("");
          }
        }, 2500);

        // ИСПРАВЛЕНИЕ: 6. ТОЛЬКО ПОТОМ запускаем таймер обратного отсчета (как в оригинале)
        this.startCountdown(button, window.AppConfig?.AD_COOLDOWN_SECONDS || 20);

        // ИСПРАВЛЕНИЕ: 7. АЛЕРТ БУДЕТ ПОКАЗАН В .then() БЛОКЕ ПОСЛЕ ВОЗВРАТА true (КАК В ОРИГИНАЛЕ!)
        console.log(`[AdsManager] ✅ НЕМЕДЛЕННАЯ ОБРАБОТКА НАГРАДЫ ЗАВЕРШЕНА, ВОЗВРАЩАЕМ true ДЛЯ ПОКАЗА АЛЕРТА`);

        // Возвращаем данные для показа алерта в .then() блоке
        return { success: true, reward: data.reward, newBalance: data.newBalance };
      } else {
        console.warn("[AdsManager] Сервер не вернул данные о награде");
        throw new Error("Награда не была начислена");
      }

    } catch (error) {
      console.error("[AdsManager] Ошибка при немедленной обработке награды:", error);
      this.handleAdError(error, button);
      return { success: false, error: error.message };
    }
  }

  /**
   * Обрабатывает успешный показ рекламы - ЗАЩИЩЕННАЯ ВЕРСИЯ
   */
  async handleAdSuccess(adType, button) {
    console.log(`[AdsManager] 🔒 ЗАЩИЩЕННАЯ обработка успешного показа: ${adType}`);

    if (window.appUtils) {
      const rewardText = window.appLocalization ? window.appLocalization.get('ads.crediting_reward') : "Начисляем награду...";
      window.appUtils.showStatus(rewardText, "info");
    }

    // КРИТИЧЕСКИ ВАЖНО: Получаем userId для системы безопасности
    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      console.error("[AdsManager] Нет данных пользователя Telegram");
      throw new Error("Нет данных пользователя для начисления награды");
    }

    // Извлекаем userId из initData
    const userId = this.extractUserIdFromInitData(initData);
    if (!userId) {
      throw new Error("Не удалось извлечь ID пользователя");
    }

    try {
      // КРИТИЧЕСКИ ВАЖНО: Используем защищенную систему начисления
      if (!window.richAdsSecurityManager) {
        console.error("[AdsManager] ❌ Система безопасности не инициализирована");
        throw new Error("Система безопасности недоступна");
      }

      // Подтверждаем награду через защищенную систему
      const response = await window.richAdsSecurityManager.confirmReward(userId, adType, {
        success: true,
        timestamp: Date.now(),
        source: 'richads_success'
      });

      if (!response.ok) {
        throw new Error(`Ошибка сервера: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // ИСПРАВЛЕНИЕ: Проверяем ответ сервера (из оригинала)
      if (data.error) {
        console.error("[AdsManager] Ошибка сервера:", data.error);
        throw new Error(data.error);
      }

      if (!data.success) {
        console.warn("[AdsManager] Сервер не подтвердил успешный просмотр");
        throw new Error("Просмотр не засчитан сервером");
      }

      // ИСПРАВЛЕНИЕ: Обновляем баланс только при успешном ответе сервера (из оригинала)
      if (data.newBalance !== undefined && data.reward > 0) {
        const coinsText4 = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        console.log(`[AdsManager] Награда подтверждена сервером: +${data.reward} ${coinsText4}`);

        // ИСПРАВЛЕНИЕ: СРАЗУ обновляем баланс в интерфейсе (как в оригинале)
        if (window.balanceManager) {
          window.balanceManager.updateBalance(data.newBalance, `ad_reward_${adType}`);
        }

        // ИСПРАВЛЕНИЕ: СРАЗУ воспроизводим звук монет при реальном начислении (как в оригинале)
        console.log(`[AdsManager] 🎵 Воспроизводим звук для ${data.reward} ${coinsText4}`);
        if (window.audioManager && window.audioManager.playCoinsSound) {
          window.audioManager.playCoinsSound(data.reward);
        } else if (window.playCoinsSound) {
          // ИСПРАВЛЕНИЕ: Fallback на глобальную функцию как в оригинале
          window.playCoinsSound(data.reward);
        } else {
          console.warn(`[AdsManager] Звук ${coinsText4} недоступен!`);
        }

        // ИСПРАВЛЕНИЕ: СРАЗУ показываем алерт об успехе (как в оригинале)
        const successMessage = window.appLocalization ?
          window.appLocalization.get('ads.ad_reward_credited') :
          `Награда за просмотр рекламы зачислена! +${data.reward} ${coinsText4}`;

        if (window.Telegram?.WebApp?.showPopup) {
          window.Telegram.WebApp.showPopup({
            title: window.appLocalization ? window.appLocalization.get('common.success') : "Успех!",
            message: successMessage,
            buttons: [{
              type: "ok",
              text: window.appLocalization ? window.appLocalization.get('common.excellent') : "Отлично"
            }]
          });
        } else if (window.Telegram?.WebApp?.showAlert) {
          window.Telegram.WebApp.showAlert(successMessage);
        } else {
          // Fallback на обычный alert
          alert(successMessage);
        }

        // Показываем статус сообщение
        if (window.appUtils) {
          const rewardStatusText = window.appLocalization ?
            window.appLocalization.get('ads.reward_status_credited').replace('{reward}', data.reward).replace('{coins}', coinsText4) :
            `🎉 Награда зачислена: +${data.reward} ${coinsText4}!`;
          window.appUtils.showStatus(rewardStatusText, "success");
        }

        // Вибрация успеха
        if (window.Telegram?.WebApp?.HapticFeedback) {
          window.Telegram.WebApp.HapticFeedback.notificationOccurred('success');
        }

        // ИСПРАВЛЕНИЕ: Запускаем таймер обратного отсчета (из оригинала)
        this.startCountdown(button, window.AppConfig?.AD_COOLDOWN_SECONDS || 20);

        console.log(`[AdsManager] ✅ Просмотр рекламы успешно обработан`);
      } else {
        console.warn("[AdsManager] Сервер не вернул данные о награде");
        throw new Error("Награда не была начислена");
      }

    } catch (error) {
      console.error("[AdsManager] Ошибка при обработке награды:", error);
      this.handleAdError(error, button);
    }
  }

  /**
   * Обрабатывает ошибку показа рекламы (из оригинала)
   */
  handleAdError(error, button) {
    console.error(`[AdsManager] Ошибка показа рекламы:`, error);

    // Логируем ошибку показа рекламы
    const adType = this.getAdTypeFromButton(button);
    this.logAdClick(adType, 'ad_error', error.message || 'Unknown error');

    const defaultErrorText = window.appLocalization ?
      window.appLocalization.get('ads.failed_to_show') :
      'Не удалось показать рекламу. Попробуйте позже.';
    let userFriendlyMessage = defaultErrorText;
    let reason = window.appLocalization ? window.appLocalization.get('ads.unknown_error') : "Неизвестная ошибка";

    // ИСПРАВЛЕНИЕ: Обработка ошибок точно как в оригинале
    if (error instanceof Error) {
      reason = error.message;

      // ИСПРАВЛЕНИЕ: Проверяем, есть ли реклама (из оригинала)
      if (reason && reason.toLowerCase().includes('no ad')) {
        this.showNoAdMessage();
        this.startCountdown(button);
        return;
      }

      // ИСПРАВЛЕНИЕ: Обработка типичных ошибок с более понятными сообщениями (из оригинала)
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length") ||
          reason.includes("updateActiveWidgetManagerByType") ||
          reason.includes("triggerInterstitialMixed")) {
        userFriendlyMessage = window.appLocalization ? window.appLocalization.get('ads.currently_unavailable') : "В данный момент реклама недоступна";
        // ИСПРАВЛЕНИЕ: Показываем сообщение об отсутствии рекламы для этих ошибок
        this.showNoAdMessage();
        this.startCountdown(button);
        return;
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = window.appLocalization ? window.appLocalization.get('ads.timeout_error') : "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = window.appLocalization ?
          window.appLocalization.get('ads.network_problem') :
          'Проблема с сетевым подключением';
      } else {
        userFriendlyMessage = defaultErrorText;
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();

      // ИСПРАВЛЕНИЕ: Проверяем, есть ли реклама (из оригинала)
      if (reason.toLowerCase().includes('no ad')) {
        this.showNoAdMessage();
        this.startCountdown(button);
        return;
      }

      userFriendlyMessage = defaultErrorText;
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = window.appLocalization ?
          window.appLocalization.get('ads.currently_unavailable') :
          'В данный момент реклама недоступна';
      } catch (e) {
        reason = window.appLocalization ? window.appLocalization.get('ads.unavailable_or_closed') : "Реклама недоступна или была закрыта";
        userFriendlyMessage = window.appLocalization ?
          window.appLocalization.get('ads.currently_unavailable') :
          'В данный момент реклама недоступна';
      }
    }

    console.warn(`[AdsManager] Причина ошибки: ${reason}`);

    // ИСПРАВЛЕНИЕ: Показываем сообщение об ошибке (из оригинала)
    if (window.appUtils) {
      window.appUtils.showStatus(userFriendlyMessage, "error");
    }

    // Вибрация предупреждения
    if (window.Telegram?.WebApp?.HapticFeedback) {
      window.Telegram.WebApp.HapticFeedback.notificationOccurred('warning');
    }

    // ИСПРАВЛЕНИЕ: Запускаем таймер даже при ошибке (из оригинала)
    this.startCountdown(button);

    // Сбрасываем флаги
    this.isButtonPressed = false;
    window.isButtonPressed = false;
  }

  /**
   * Показывает сообщение об отсутствии рекламы (из оригинала)
   */
  showNoAdMessage() {
    const message = window.appLocalization ?
      window.appLocalization.get('ads.no_ads_available') :
      "Нет доступной рекламы. Попробуйте позже.";

    // Логируем случай отсутствия рекламы
    const adType = this.getAdTypeFromButton(this.currentButton);
    this.logAdClick(adType, 'no_ads_available', 'No ads available from ad network');

    if (window.appUtils) {
      window.appUtils.showStatus(message, "info");
    }

    console.log("[AdsManager] Показано сообщение об отсутствии рекламы");
  }

  /**
   * Запускает счетчик обратного отсчета на кнопке (ТОЧНО КАК В ОРИГИНАЛЕ)
   * @param {HTMLElement} button - Кнопка для блокировки
   * @param {number} seconds - Количество секунд для отсчета (по умолчанию 20)
   */
  startCountdown(button, seconds = 20) {
    if (!button) {
      console.warn(`[AdsManagerFull] ❌ Кнопка не найдена для таймера`);
      return;
    }

    console.log(`[AdsManagerFull] 🕐 ЗАПУСК ТАЙМЕРА на ${seconds} секунд для кнопки ${button.id}`);
    console.log(`[AdsManagerFull] 📊 isButtonPressed до запуска: ${this.isButtonPressed}`);

    // ИСПРАВЛЕНИЕ: НЕ проверяем isButtonPressed - таймер должен запускаться ВСЕГДА после рекламы
    this.isButtonPressed = true;

    // Добавляем класс нажатой кнопки
    button.classList.add('pressed');

    // Блокируем конкретную кнопку с таймером
    button.disabled = true;
    button.style.pointerEvents = 'none'; // Дополнительная защита от кликов

    // Блокируем все кнопки рекламы
    this.disableAllAdButtons();

    // Создаем элемент счетчика
    const countdownOverlay = document.createElement('div');
    countdownOverlay.className = 'countdown-overlay';

    // ПРИНУДИТЕЛЬНЫЕ СТИЛИ для видимости
    countdownOverlay.style.cssText = `
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: rgba(0, 0, 0, 0.9) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border-radius: 12px !important;
      z-index: 9999 !important;
      pointer-events: all !important;
      cursor: not-allowed !important;
    `;

    // Создаем элемент для цифр с улучшенной контрастностью
    const countdownTime = document.createElement('span');
    countdownTime.className = 'countdown-time';
    countdownTime.textContent = seconds;

    // ПРИНУДИТЕЛЬНЫЕ СТИЛИ для цифр
    countdownTime.style.cssText = `
      color: #FFFFFF !important;
      font-size: 32px !important;
      font-weight: 900 !important;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 1) !important;
      pointer-events: none !important;
      font-family: Arial, sans-serif !important;
      z-index: 10000 !important;
    `;

    countdownOverlay.appendChild(countdownTime);

    // ПРИНУДИТЕЛЬНО устанавливаем position: relative для кнопки
    const originalPosition = button.style.position;
    button.style.position = 'relative';

    button.appendChild(countdownOverlay);
    console.log(`[AdsManagerFull] ✅ Overlay добавлен к кнопке ${button.id}`);
    console.log(`[AdsManagerFull] 📊 Overlay стили:`, countdownOverlay.style.cssText);
    console.log(`[AdsManagerFull] 📊 Цифры стили:`, countdownTime.style.cssText);

    let remainingTime = seconds;
    console.log(`[AdsManagerFull] 🕐 Начальное время: ${remainingTime}`);

    this.countdownTimer = setInterval(() => {
      remainingTime--;
      countdownTime.textContent = remainingTime;
      console.log(`[AdsManagerFull] ⏰ Таймер: ${remainingTime} секунд (текст: "${countdownTime.textContent}")`);

      // Проверяем что элемент все еще на месте
      if (!button.contains(countdownOverlay)) {
        console.error(`[AdsManagerFull] ❌ Overlay пропал из кнопки ${button.id}!`);
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
        this.isButtonPressed = false;
        return;
      }

      if (remainingTime <= 0) {
        console.log(`[AdsManagerFull] 🏁 Таймер завершен, очищаем...`);
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;

        // Убираем счетчик и возвращаем кнопку в исходное состояние
        try {
          if (button.contains(countdownOverlay)) {
            button.removeChild(countdownOverlay);
            console.log(`[AdsManagerFull] ✅ Overlay удален из кнопки ${button.id}`);
          } else {
            console.warn(`[AdsManagerFull] ⚠️ Overlay уже не в кнопке ${button.id}`);
          }
        } catch (error) {
          console.error(`[AdsManagerFull] ❌ Ошибка удаления overlay:`, error);
        }

        button.classList.remove('pressed');

        // Восстанавливаем оригинальную позицию кнопки
        button.style.position = originalPosition || '';

        // Разблокируем конкретную кнопку с таймером
        button.disabled = false;
        button.style.pointerEvents = 'auto'; // Восстанавливаем возможность кликов

        // Разблокируем все кнопки рекламы
        this.enableAllAdButtons();

        this.isButtonPressed = false;

        console.log(`[AdsManagerFull] ✅ Таймер завершен, кнопка ${button.id} разблокирована`);

        // 🔄 АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ РЕКЛАМНЫХ БЛОКОВ (из оригинала)
        if (window.AppConfig?.AUTO_RELOAD_AFTER_COUNTDOWN) {
          if (window.AppConfig?.USE_SOFT_REFRESH) {
            // Мягкое обновление - переинициализация SDK без reload страницы (из оригинала)
            console.log("[AdsManager] Таймер завершен, выполняем мягкое обновление рекламы");
            setTimeout(() => {
              this.refreshAdBlocks();
            }, 500);
          } else {
            // Полный reload страницы (из оригинала)
            console.log("[AdsManager] Таймер завершен, выполняем reload страницы для обновления рекламы");

            // Показываем уведомление о reload (из оригинала)
            if (window.appUtils) {
              const updatingText = window.appLocalization ? window.appLocalization.get('status.updating_ads') : "Обновляем рекламные блоки...";
              window.appUtils.showStatus(updatingText, "info");
            }

            // Небольшая задержка перед reload для показа сообщения (из оригинала)
            setTimeout(() => {
              try {
                // Сохраняем текущую страницу в localStorage перед reload (из оригинала)
                if (window.pageManager) {
                  window.pageManager.saveCurrentPage();
                }

                // Выполняем reload страницы (из оригинала)
                window.location.reload();
              } catch (error) {
                console.warn("[AdsManager] Ошибка при reload:", error);
                // Если reload не удался, пробуем мягкое обновление (из оригинала)
                this.refreshAdBlocks();
              }
            }, 1000);
          }
        } else {
          // ИСПРАВЛЕНИЕ: Если автообновление отключено, просто показываем сообщение
          console.log("[AdsManager] Автообновление отключено, кнопки уже включены");
          if (window.appUtils) {
            const adsAvailableText = window.appLocalization ? window.appLocalization.get('ads.ads_available_again') : "Реклама снова доступна!";
            window.appUtils.showStatus(adsAvailableText, "success");
            setTimeout(() => {
              const checkText = window.appLocalization ? window.appLocalization.get('ads.ads_available_again') : "Реклама снова доступна!";
              if (window.appUtils.statusMessageEl?.textContent === checkText) {
                window.appUtils.showStatus("", "");
              }
            }, 2000);
          }
        }
      }
    }, 1000);

    console.log(`[AdsManager] ✅ Таймер запущен на ${seconds} секунд`);
  }

  /**
   * Принудительно очищает таймер и разблокирует кнопки
   */
  clearCountdown() {
    console.log('[AdsManagerFull] 🔄 ПРИНУДИТЕЛЬНАЯ ОЧИСТКА ТАЙМЕРА');

    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
      console.log('[AdsManagerFull] ✅ Таймер принудительно очищен');
    }

    // Разблокируем только рекламные кнопки
    this.enableAllAdButtons();
    this.isButtonPressed = false;
    this.globalClickBlocked = false;
    window.adsBlocked = false;

    console.log('[AdsManagerFull] ✅ Рекламные кнопки разблокированы, навигация не затронута');

    // Убираем overlay со всех кнопок
    const buttons = [
      document.getElementById('openLinkButton'),
      document.getElementById('watchVideoButton'),
      document.getElementById('openAdButton')
    ];

    buttons.forEach(button => {
      if (button) {
        const overlay = button.querySelector('.countdown-overlay');
        if (overlay) {
          try {
            button.removeChild(overlay);
            console.log(`[AdsManagerFull] ✅ Overlay удален с кнопки ${button.id}`);
          } catch (error) {
            console.warn(`[AdsManagerFull] ⚠️ Ошибка удаления overlay с ${button.id}:`, error);
          }
        }
        button.classList.remove('pressed');
        button.style.position = '';
        button.disabled = false;
        button.style.pointerEvents = 'auto';
        button.style.opacity = '1';
        button.style.cursor = 'pointer';
      }
    });

    console.log('[AdsManagerFull] ✅ Принудительная очистка завершена');
  }

  /**
   * Записывает просмотр рекламы (из оригинала)
   */
  async recordAdView(adType) {
    console.log(`[AdsManager] Запись просмотра рекламы: ${adType}`);

    // Получаем данные пользователя
    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      console.warn('[AdsManager] Нет данных пользователя для записи просмотра');
      return { success: false, error: 'Нет данных пользователя' };
    }

    try {
      // Отправляем запрос на сервер для записи просмотра рекламы
      // БЕЗОПАСНОСТЬ: API должен быть настроен, иначе блокируем
      if (!window.API_BASE_URL) {
        throw new Error("API сервера не настроен - реклама недоступна");
      }

      const response = await fetch(`${window.API_BASE_URL}/recordAdView.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ initData, adType }),
      });

      if (!response.ok) {
        throw new Error(`Ошибка сервера: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      console.log(`[AdsManager] Просмотр записан: ${adType}, награда: ${data.reward}`);
      return { success: true, data };

    } catch (error) {
      console.warn('[AdsManager] Ошибка записи просмотра:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Обновляет рекламные блоки (из оригинала)
   */
  refreshAdBlocks() {
    try {
      // Показываем статус обновления (из оригинала)
      if (window.appUtils) {
        const refreshingText = window.appLocalization ? window.appLocalization.get('ads.refreshing_ads') : "Обновляем рекламу...";
        window.appUtils.showStatus(refreshingText, "info");
      }

      // Если есть активный контроллер рекламы, пробуем его переинициализировать (из оригинала)
      if (this.adsController && typeof this.adsController.initialize === 'function') {
        console.log("[AdsManager] Переинициализация RichAds SDK...");

        this.adsController.initialize({
          pubId: window.AppConfig?.MY_PUB_ID,
          appId: window.AppConfig?.MY_APP_ID,
          debug: window.AppConfig?.DEBUG_MODE,
        });

        setTimeout(() => {
          if (window.appUtils) {
            const adsUpdatedText = window.appLocalization ? window.appLocalization.get('ads.ads_updated') : "Реклама обновлена!";
            window.appUtils.showStatus(adsUpdatedText, "success");
            setTimeout(() => {
              const checkUpdatedText = window.appLocalization ? window.appLocalization.get('ads.ads_updated') : "Реклама обновлена!";
              if (window.appUtils.statusMessageEl?.textContent === checkUpdatedText) {
                window.appUtils.showStatus("", "");
              }
            }, 2000);
          }

          // ИСПРАВЛЕНИЕ: Включаем все кнопки рекламы после успешного обновления
          console.log("[AdsManager] 🔓 Включаем кнопки рекламы после обновления блоков");
          this.enableAllAdButtons();
        }, 1000);

      } else {
        // Если контроллер недоступен, пробуем полную переинициализацию (из оригинала)
        console.log("[AdsManager] Полная переинициализация рекламы...");
        this.initializeRichAds();

        // ИСПРАВЛЕНИЕ: Включаем кнопки после переинициализации (с задержкой)
        setTimeout(() => {
          console.log("[AdsManager] 🔓 Включаем кнопки рекламы после переинициализации");
          this.enableAllAdButtons();
        }, 2000);
      }

    } catch (error) {
      console.warn("[AdsManager] Ошибка обновления рекламных блоков:", error);
      if (window.appUtils) {
        const updateErrorText = window.appLocalization ?
          window.appLocalization.get('ads.update_error') :
          'Ошибка обновления рекламы';
        window.appUtils.showStatus(updateErrorText, "error");
        setTimeout(() => {
          window.appUtils.showStatus("", "");
        }, 3000);
      }

      // ИСПРАВЛЕНИЕ: Включаем кнопки даже при ошибке обновления (чтобы не заблокировать навсегда)
      setTimeout(() => {
        console.log("[AdsManager] 🔓 Включаем кнопки рекламы после ошибки обновления");
        this.enableAllAdButtons();
      }, 3000);
    }
  }

  /**
   * Обновляет рекламу (мягкое обновление) (из оригинала)
   */
  refreshAds() {
    this.refreshAdBlocks();
  }
}

// Экспорт для обратной совместимости
window.adsManagerFull = new AdsManagerFull();

// Экспорт функций из оригинала
window.initializeRichAds = () => window.adsManagerFull.initializeRichAds();
// Создаем глобальный экземпляр
window.adsManagerFull = new AdsManagerFull();

window.handleWatchAdClick = () => window.adsManagerFull.handleWatchAdClick();
window.handleWatchVideoClick = () => window.adsManagerFull.handleWatchVideoClick();
window.handleOpenLinkClick = () => window.adsManagerFull.handleOpenLinkClick();
window.recordAdView = (adType) => window.adsManagerFull.recordAdView(adType);
window.refreshAds = () => window.adsManagerFull.refreshAds();

console.log('📺 [AdsManagerFull] Полный менеджер рекламы загружен.');
