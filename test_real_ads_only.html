<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Тест ТОЛЬКО реальной рекламы (без эмуляций)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🔒 Тест ТОЛЬКО реальной рекламы</h1>
    <p><strong>ВАЖНО:</strong> Эта страница тестирует систему БЕЗ эмуляций. Реклама работает только при наличии RichAds SDK.</p>

    <div class="test-section">
        <h3>🔍 Проверка системы безопасности</h3>
        <div id="securityCheck">Проверяем...</div>
    </div>

    <div class="test-section">
        <h3>📊 Состояние RichAds SDK</h3>
        <div id="richAdsStatus">Загрузка...</div>
        <button class="test-button" onclick="checkRichAdsStatus()">🔄 Проверить статус</button>
    </div>

    <div class="test-section">
        <h3>🎯 Тест реальной рекламы</h3>
        <p>Эти кнопки работают ТОЛЬКО с настоящим RichAds SDK:</p>
        
        <button id="testNativeBanner" class="test-button" onclick="testRealAd('native_banner')">
            🔗 Тест нативного баннера
        </button>
        
        <button id="testRewardedVideo" class="test-button" onclick="testRealAd('rewarded_video')">
            📺 Тест видеорекламы
        </button>
        
        <button id="testInterstitial" class="test-button" onclick="testRealAd('interstitial')">
            💰 Тест интерстициальной рекламы
        </button>
    </div>

    <div class="test-section">
        <h3>📝 Лог тестирования</h3>
        <div id="testLog" class="log"></div>
        <button class="test-button" onclick="clearLog()">🗑️ Очистить лог</button>
    </div>

    <div id="statusMessage"></div>

    <!-- Загружаем только необходимые модули -->
    <script src="js/richads-security.js"></script>
    <script>
        // Настройка для тестирования
        window.API_BASE_URL = 'api';
        
        // Эмуляция Telegram WebApp для локального тестирования
        if (!window.Telegram) {
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test',
                    ready: () => {},
                    expand: () => {},
                    close: () => {}
                }
            };
        }

        function addToLog(message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        function checkSecuritySystem() {
            const securityDiv = document.getElementById('securityCheck');
            let html = '<h4>🔒 Проверка системы безопасности:</h4><ul>';
            
            // Проверяем отсутствие эмуляций
            const hasEmulation = checkForEmulations();
            if (hasEmulation.length === 0) {
                html += '<li style="color: #28a745;">✅ Эмуляции рекламы отсутствуют</li>';
            } else {
                html += '<li style="color: #dc3545;">❌ Найдены эмуляции: ' + hasEmulation.join(', ') + '</li>';
            }
            
            // Проверяем систему безопасности
            if (window.richAdsSecurityManager) {
                html += '<li style="color: #28a745;">✅ Система безопасности загружена</li>';
            } else {
                html += '<li style="color: #dc3545;">❌ Система безопасности не найдена</li>';
            }
            
            // Проверяем защищенный API
            html += '<li style="color: #28a745;">✅ Защищенный API: secure_reward.php</li>';
            
            html += '</ul>';
            securityDiv.innerHTML = html;
        }

        function checkForEmulations() {
            const emulations = [];
            
            // Проверяем глобальные объекты на наличие эмуляций
            if (window.adsController && window.adsController.emulated) {
                emulations.push('adsController.emulated');
            }
            
            // Проверяем методы на эмуляцию
            if (window.adsController && typeof window.adsController.triggerInterstitialBanner === 'function') {
                const methodStr = window.adsController.triggerInterstitialBanner.toString();
                if (methodStr.includes('emulated') || methodStr.includes('Promise.resolve')) {
                    emulations.push('triggerInterstitialBanner эмуляция');
                }
            }
            
            return emulations;
        }

        function checkRichAdsStatus() {
            const statusDiv = document.getElementById('richAdsStatus');
            let html = '<h4>📊 Статус RichAds SDK:</h4><ul>';
            
            // Проверяем наличие Telegram WebApp
            if (window.Telegram?.WebApp) {
                html += '<li style="color: #28a745;">✅ Telegram WebApp доступен</li>';
            } else {
                html += '<li style="color: #dc3545;">❌ Telegram WebApp недоступен</li>';
            }
            
            // Проверяем загрузку RichAds SDK
            if (window.TelegramAdsController) {
                html += '<li style="color: #28a745;">✅ RichAds SDK загружен</li>';
            } else {
                html += '<li style="color: #dc3545;">❌ RichAds SDK не загружен</li>';
            }
            
            // Проверяем контроллер рекламы
            if (window.adsManagerFull?.adsController) {
                html += '<li style="color: #28a745;">✅ Контроллер рекламы инициализирован</li>';
            } else {
                html += '<li style="color: #dc3545;">❌ Контроллер рекламы не найден</li>';
            }
            
            html += '</ul>';
            statusDiv.innerHTML = html;
            
            // Обновляем состояние кнопок
            updateButtonStates();
        }

        function updateButtonStates() {
            const buttons = ['testNativeBanner', 'testRewardedVideo', 'testInterstitial'];
            const isRichAdsAvailable = window.TelegramAdsController && window.adsManagerFull?.adsController;
            
            buttons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.disabled = !isRichAdsAvailable;
                    if (!isRichAdsAvailable) {
                        button.title = 'RichAds SDK недоступен';
                    } else {
                        button.title = 'Тест реальной рекламы';
                    }
                }
            });
        }

        async function testRealAd(adType) {
            addToLog(`🎯 Тестируем РЕАЛЬНУЮ рекламу: ${adType}`);
            
            try {
                // Проверяем доступность RichAds
                if (!window.TelegramAdsController) {
                    throw new Error('RichAds SDK не загружен');
                }
                
                if (!window.adsManagerFull?.adsController) {
                    throw new Error('Контроллер рекламы не инициализирован');
                }
                
                // Регистрируем ожидающую награду
                if (window.richAdsSecurityManager) {
                    const rewardData = window.richAdsSecurityManager.registerPendingReward(12345, adType);
                    addToLog(`✅ Зарегистрирована ожидающая награда: ${rewardData.rewardId}`);
                }
                
                // Вызываем РЕАЛЬНЫЙ метод RichAds
                let result;
                switch (adType) {
                    case 'native_banner':
                        result = await window.adsManagerFull.adsController.triggerInterstitialBanner(true);
                        break;
                    case 'rewarded_video':
                        result = await window.adsManagerFull.adsController.triggerInterstitialBanner();
                        break;
                    case 'interstitial':
                        result = await window.adsManagerFull.adsController.triggerInterstitialBanner(false);
                        break;
                    default:
                        throw new Error('Неизвестный тип рекламы');
                }
                
                addToLog(`📊 Результат RichAds: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    // Подтверждаем награду через защищенную систему
                    if (window.richAdsSecurityManager) {
                        const rewardResult = await window.richAdsSecurityManager.confirmReward(12345, adType, result);
                        addToLog(`🎉 Награда начислена: ${rewardResult.reward} монет`);
                        showStatus(`✅ Реальная реклама ${adType} показана успешно!`, 'success');
                    }
                } else {
                    addToLog(`❌ RichAds вернул неуспешный результат`);
                    showStatus(`❌ Ошибка показа рекламы ${adType}`, 'error');
                }
                
            } catch (error) {
                addToLog(`❌ Ошибка теста реальной рекламы: ${error.message}`);
                showStatus(`❌ Ошибка: ${error.message}`, 'error');
            }
        }

        // Инициализация при загрузке
        window.addEventListener('load', () => {
            addToLog('🔒 Система тестирования ТОЛЬКО реальной рекламы загружена');
            checkSecuritySystem();
            checkRichAdsStatus();
            
            // Обновляем статус каждые 5 секунд
            setInterval(() => {
                checkRichAdsStatus();
            }, 5000);
        });
    </script>
</body>
</html>
