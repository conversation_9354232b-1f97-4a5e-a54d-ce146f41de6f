/**
 * main-fixed.js
 * ИСПРАВЛЕННАЯ МОДУЛЬНАЯ ВЕРСИЯ - Основной файл приложения UniQPaid
 * Восстанавливает всю функциональность из оригинала
 */

// Импорт констант из конфигурации
import {
  API_BASE_URL,
  // БЕЗОПАСНОСТЬ: DEBUG_MODE удален
  BOT_USERNAME,
  MY_PUB_ID,
  MY_APP_ID,
  AD_TYPES,
  updateCurrentUserId,
  updateAdsController,
  appSettings,
  calculatorData
} from './config.js';

// Глобальные переменные
let currentUserId = null;
let isAppInitialized = false;
let adsController = null;
let currentPageElement = null;
let isTransitioning = false;

// Переменные для отслеживания состояния рекламы
let lastAdShownTime = 0;
let adCooldownTime = 3000;
let isAdShowing = false;
let countdownTimer = null;
let isButtonPressed = false;
let coinValue = 0.001;

/**
 * Отображает статусное сообщение
 */
function showStatus(message, type = "info") {
  const statusMessageEl = document.getElementById("status-message");
  if (!statusMessageEl) {
    console.warn("Элемент status-message не найден.");
    return;
  }

  statusMessageEl.textContent = message;
  statusMessageEl.className = `status-message ${type}`;

  if (message) {
    statusMessageEl.style.display = "block";
  } else {
    statusMessageEl.style.display = "none";
  }
}

/**
 * Инициализирует Telegram WebApp
 */
function initTelegramWebApp() {
  try {
    const tg = window.Telegram?.WebApp;
    if (!tg) {
      console.warn("Telegram WebApp недоступен");
      return false;
    }

    tg.ready();
    tg.expand();
    
    // Получаем данные пользователя
    const user = tg.initDataUnsafe?.user;
    if (user) {
      currentUserId = user.id.toString();
      
      // Отображаем имя пользователя
      const userNameEl = document.getElementById("user-name");
      if (userNameEl) {
        const displayName = user.first_name + (user.last_name ? ` ${user.last_name}` : '');
        userNameEl.textContent = displayName;
      }
      
      console.log(`👤 Пользователь: ${user.first_name} (ID: ${currentUserId})`);
      return true;
    } else {
      console.warn("Данные пользователя Telegram недоступны");
      return false;
    }
  } catch (error) {
    console.error("Ошибка инициализации Telegram WebApp:", error);
    return false;
  }
}

/**
 * Загружает модуль с обработкой ошибок
 */
async function loadModule(modulePath, moduleName) {
  try {
    console.log(`📦 Загружаем модуль: ${moduleName}`);
    const module = await import(modulePath);
    console.log(`✅ Модуль ${moduleName} загружен`);
    return module;
  } catch (error) {
    console.error(`❌ Ошибка загрузки модуля ${moduleName}:`, error);
    return null;
  }
}

/**
 * Инициализирует базовые модули
 */
async function initCoreModules() {
  try {
    // Загружаем конфигурацию
    const configModule = await loadModule('./config.js', 'Конфигурация');
    if (configModule) {
      // Обновляем глобальные настройки
      if (configModule.updateCurrentUserId && currentUserId) {
        configModule.updateCurrentUserId(currentUserId);
      }
    }

    // Загружаем управление балансом
    const balanceModule = await loadModule('./balance-manager.js', 'Управление балансом');
    if (balanceModule && balanceModule.updateBalanceDisplay) {
      // Устанавливаем начальный баланс (0 для продакшена)
      balanceModule.updateBalanceDisplay(0);
    }

    // Загружаем навигацию
    const navigationModule = await loadModule('./navigation.js', 'Навигация');
    if (navigationModule && navigationModule.initNavigation) {
      navigationModule.initNavigation();
    }

    // Загружаем калькулятор
    const calculatorModule = await loadModule('./calculator.js', 'Калькулятор');
    if (calculatorModule && calculatorModule.initCalculator) {
      calculatorModule.initCalculator();
    }

    // Загружаем систему вывода
    const withdrawalModule = await loadModule('./withdrawal.js', 'Система вывода');
    if (withdrawalModule && withdrawalModule.initWithdrawal) {
      withdrawalModule.initWithdrawal();
    }

    console.log("✅ Базовые модули инициализированы");
    return true;
  } catch (error) {
    console.error("❌ Ошибка инициализации базовых модулей:", error);
    return false;
  }
}

/**
 * Инициализирует дополнительные модули
 */
async function initOptionalModules() {
  try {
    // ИСПРАВЛЕНИЕ: Сначала загружаем аудио-менеджер для звука монеток
    await loadModule('./audio-manager.js', 'Аудио менеджер');
    console.log("🎵 Аудио менеджер загружен");

    // Загружаем реферальную систему
    const referralModule = await loadModule('./referral-system.js', 'Реферальная система');
    if (referralModule && referralModule.initReferralSystem) {
      referralModule.initReferralSystem();
    }

    // ОТКЛЮЧЕНО: Система рекламы теперь в ads-manager-full.js (загружается через modules-loader.js)
    console.log("📺 Система рекламы: используется ads-manager-full.js");

    // Загружаем дополнительные модули
    await loadModule('./localization.js', 'Локализация');
    await loadModule('./design-loader.js', 'Загрузчик дизайна');
    await loadModule('./audio-manager.js', 'Аудио менеджер');

    console.log("✅ Дополнительные модули загружены");
  } catch (error) {
    console.warn("⚠️ Ошибка загрузки дополнительных модулей:", error);
  }
}

/**
 * Показывает главную страницу
 */
function showMainPage() {
  try {
    // Находим и активируем главную страницу
    const earnPage = document.getElementById('earn-page');
    const mainContent = document.getElementById('main-content');
    
    // Скрываем все страницы
    document.querySelectorAll('.page, .app-section').forEach(page => {
      page.classList.remove('active', 'active-section');
      page.classList.add('page-hidden');
    });
    
    // Деактивируем все кнопки навигации
    document.querySelectorAll('.nav-button').forEach(button => {
      button.classList.remove('active');
    });
    
    // Показываем главную страницу
    if (mainContent) {
      mainContent.classList.add('active-section');
      mainContent.classList.remove('page-hidden');
    }
    
    // Активируем кнопку навигации
    const navHomeButton = document.querySelector('[data-page="main-content"], #nav-home');
    if (navHomeButton) {
      navHomeButton.classList.add('active');
    }
    
    console.log("🏠 Главная страница активирована");
  } catch (error) {
    console.error("❌ Ошибка активации главной страницы:", error);
  }
}

/**
 * Инициализирует RichAds SDK
 */
function initializeRichAds() {
  showStatus("Инициализация рекламы...");
  try {
    console.log("[RichAds Init] Проверка доступности SDK...");

    if (typeof TelegramAdsController !== "function" && typeof TelegramAdsController !== "object") {
      console.warn("[RichAds Init] SDK не найден, пробуем загрузить скрипт вручную");

      const script = document.createElement('script');
      script.src = "https://richinfo.co/richpartners/telegram/js/tg-ob.js";
      document.head.appendChild(script);

      setTimeout(() => {
        console.log("[RichAds Init] Повторная попытка инициализации после загрузки скрипта");
        initializeRichAdsAfterLoad();
      }, 1000);

      return;
    }

    initializeRichAdsAfterLoad();
  } catch (error) {
    console.error("[RichAds Init] КРИТИЧЕСКАЯ ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");

    const tg = window.Telegram?.WebApp;
    if (tg) {
      tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);
    }

    disableAllAdButtons();
  }
}

/**
 * Инициализирует RichAds SDK после загрузки скрипта
 */
function initializeRichAdsAfterLoad() {
  try {
    console.log("[RichAds Init] Попытка создания экземпляра SDK...");

    if (typeof TelegramAdsController === "function") {
      console.log("[RichAds Init] SDK доступен как функция, создаем экземпляр");
      adsController = new TelegramAdsController();
      window.TelegramAdsController = adsController;
    } else if (typeof TelegramAdsController === "object" && TelegramAdsController !== null) {
      console.log("[RichAds Init] SDK доступен как объект, используем его");
      adsController = TelegramAdsController;
    } else {
      throw new Error("TelegramAdsController недоступен");
    }

    console.log("[RichAds Init] Инициализация SDK...");
    adsController.initialize({
      pubId: MY_PUB_ID,
      appId: MY_APP_ID
      // БЕЗОПАСНОСТЬ: debug режим удален
    });

    console.log("[RichAds Init] Инициализация SDK успешно вызвана");
    showStatus("Реклама готова.", "success");

    updateAdsController(adsController);
    enableAllAdButtons();

    setTimeout(() => {
      const statusMessageEl = document.getElementById("status-message");
      if (statusMessageEl && statusMessageEl.textContent === "Реклама готова.") {
        showStatus("");
      }
    }, 2000);
  } catch (error) {
    console.error("[RichAds Init] ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");

    const tg = window.Telegram?.WebApp;
    if (tg) {
      tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);
    }

    disableAllAdButtons();
  }
}

/**
 * Блокирует все кнопки рекламы
 */
function disableAllAdButtons() {
  const watchAdButton = document.getElementById("openLinkButton");
  const watchVideoButton = document.getElementById("watchVideoButton");
  const openLinkButton = document.getElementById("openAdButton");

  if (watchAdButton) {
    watchAdButton.disabled = true;
    watchAdButton.style.pointerEvents = 'none';
  }
  if (watchVideoButton) {
    watchVideoButton.disabled = true;
    watchVideoButton.style.pointerEvents = 'none';
  }
  if (openLinkButton) {
    openLinkButton.disabled = true;
    openLinkButton.style.pointerEvents = 'none';
  }
}

/**
 * Разблокирует все кнопки рекламы
 */
function enableAllAdButtons() {
  const watchAdButton = document.getElementById("openLinkButton");
  const watchVideoButton = document.getElementById("watchVideoButton");
  const openLinkButton = document.getElementById("openAdButton");

  if (watchAdButton) {
    watchAdButton.disabled = false;
    watchAdButton.style.pointerEvents = 'auto';
  }
  if (watchVideoButton) {
    watchVideoButton.disabled = false;
    watchVideoButton.style.pointerEvents = 'auto';
  }
  if (openLinkButton) {
    openLinkButton.disabled = false;
    openLinkButton.style.pointerEvents = 'auto';
  }
}

/**
 * Основная функция инициализации приложения
 */
async function initApp() {
  try {
    console.log("🚀 Запуск исправленного модульного приложения UniQPaid...");
    showStatus("Инициализация приложения...", "info");
    
    // 1. Инициализация Telegram WebApp
    const telegramInitialized = initTelegramWebApp();
    if (!telegramInitialized) {
      console.error("❌ Telegram WebApp не инициализирован - приложение недоступно");
      showStatus("Приложение доступно только в Telegram", "error");
      return; // Блокируем дальнейшую инициализацию
    }

    showStatus("Загрузка модулей...", "info");

    // 2. Инициализация базовых модулей
    const coreModulesLoaded = await initCoreModules();
    if (!coreModulesLoaded) {
      throw new Error("Не удалось загрузить базовые модули");
    }

    showStatus("Загрузка дополнительных компонентов...", "info");

    // 3. Инициализация дополнительных модулей
    await initOptionalModules();

    // 4. Инициализация RichAds SDK
    initializeRichAds();

    // 5. Инициализация обработчиков событий
    initEventHandlers();

    // 6. Показываем главную страницу
    showMainPage();

    // 7. Финализация
    isAppInitialized = true;
    console.log("✅ Исправленное модульное приложение UniQPaid успешно инициализировано!");
    showStatus("Приложение готово к работе", "success");

    setTimeout(() => {
      showStatus("");
    }, 3000);

  } catch (error) {
    console.error("❌ Критическая ошибка инициализации приложения:", error);
    showStatus("Ошибка инициализации приложения", "error");
    
    // Показываем детали ошибки в консоли
    console.error("Детали ошибки:", {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
  }
}

/**
 * Инициализирует обработчики событий
 */
function initEventHandlers() {
  try {
    // ОТКЛЮЧЕНО: Обработчики кнопок рекламы теперь в ads-manager-full.js
    // Дублирующие обработчики отключены для предотвращения множественных запросов
    console.log('[main-fixed.js] Обработчики рекламы отключены - используется ads-manager-full.js');

    // Обработчики навигации
    const navHomeButton = document.getElementById("nav-home");
    const navEarnButton = document.getElementById("nav-earn");
    const navFriendsButton = document.getElementById("nav-friends");

    if (navHomeButton) {
      navHomeButton.addEventListener("click", () => showMainContent());
    }
    if (navEarnButton) {
      navEarnButton.addEventListener("click", () => showEarnSection());
    }
    if (navFriendsButton) {
      navFriendsButton.addEventListener("click", () => showFriendsSection());
    }

    // Обработчики формы вывода
    const cryptoCurrencySelect = document.getElementById("crypto-currency");
    const withdrawalAmountInput = document.getElementById("withdrawal-amount");
    const requestWithdrawalButton = document.getElementById("request-withdrawal-button");

    if (cryptoCurrencySelect) {
      cryptoCurrencySelect.addEventListener("change", updateAddressPlaceholder);
    }
    if (withdrawalAmountInput) {
      withdrawalAmountInput.addEventListener("input", validateWithdrawalForm);
    }
    if (requestWithdrawalButton) {
      requestWithdrawalButton.addEventListener("click", handleRequestWithdrawal);
    }

    // Обработчики реферальной системы
    const shareAppButton = document.getElementById("share-app-button");
    const copyReferralButton = document.getElementById("copy-referral-button");

    if (shareAppButton) {
      shareAppButton.addEventListener("click", handleShareAppClick);
    }
    if (copyReferralButton) {
      copyReferralButton.addEventListener("click", copyReferralLink);
    }

    console.log("🎮 Обработчики событий инициализированы");
  } catch (error) {
    console.error("❌ Ошибка инициализации обработчиков событий:", error);
  }
}

/**
 * ОТКЛЮЧЕНО: Обрабатывает нажатие на кнопку "Открыть ссылку"
 * Функция перенесена в ads-manager-full.js
 */
function handleWatchAdClick_DISABLED() {
  const watchAdButton = document.getElementById("openLinkButton");

  if (isButtonPressed || (watchAdButton && watchAdButton.disabled)) {
    console.warn("[RichAds] Кнопка заблокирована или идет таймер - игнорируем клик");
    return;
  }

  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    const tg = window.Telegram?.WebApp;
    if (tg) {
      tg.showAlert("Модуль рекламы не загружен.");
    }
    return;
  }

  if (isTransitioning) {
    console.warn("Клик по рекламе во время перехода страницы - игнорируем.");
    return;
  }

  const currentTime = Date.now();
  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn(`[RichAds] Кулдаун активен. Осталось: ${adCooldownTime - (currentTime - lastAdShownTime)}ms`);
    return;
  }

  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос баннера-превью с автопереходом...");
  if (watchAdButton) watchAdButton.disabled = true;

  const tg = window.Telegram?.WebApp;
  if (tg && tg.HapticFeedback) {
    tg.HapticFeedback.impactOccurred("light");
  }

  try {
    console.log("[RichAds] Запуск баннера-превью с автопереходом...");

    if (typeof adsController.triggerNativeNotification === 'function') {
      console.log("[RichAds] Вызов triggerNativeNotification(true) для автоперехода по баннеру-превью");

      adsController.triggerNativeNotification(true)
        .then((result) => {
          console.log("[RichAds] Успешный автопереход по баннеру-превью:", result);
          showStatus("Баннер-превью просмотрен! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            startCountdown(watchAdButton);
            if (tg) {
              tg.showPopup({
                title: "Успех!",
                message: "Награда за переход по баннеру-превью зачислена!",
                buttons: [{ type: "ok", text: "Отлично" }]
              });
            }
          } else {
            startCountdown(watchAdButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка автоперехода по баннеру-превью:", error);
          handleAdError(error);
          startCountdown(watchAdButton);
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[RichAds] Операция автоперехода по баннеру-превью завершена");
          isAdShowing = false;
        });
    } else {
      throw new Error("Методы показа баннера-превью недоступны");
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове баннера-превью:", error);
    showStatus(`Ошибка показа баннера-превью: ${error.message}`, "error");
    if (tg) {
      tg.showAlert(`Ошибка показа баннера-превью: ${error.message}`);
    }
    if (watchAdButton) watchAdButton.disabled = false;
    isAdShowing = false;
  }
}

/**
 * Обрабатывает ошибки рекламы
 */
function handleAdError(error) {
  console.warn("[RichAds] Ошибка показа рекламы:", error);
  let userFriendlyMessage = "В данный момент реклама недоступна";

  if (error instanceof Error) {
    const reason = error.message;

    if (reason.includes("Cannot read properties of null") ||
        reason.includes("undefined") ||
        reason.includes("length")) {
      userFriendlyMessage = "В данный момент реклама недоступна";
    } else if (reason.includes("timeout") || reason.includes("time out")) {
      userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
    } else if (reason.includes("network") || reason.includes("connection")) {
      userFriendlyMessage = "Проблемы с сетевым соединением";
    }
  }

  showStatus(userFriendlyMessage, "error");
}

/**
 * Проверяет готовность DOM и запускает приложение
 */
function startApp() {
  console.log("🔍 Проверка готовности DOM...");
  
  // Проверяем наличие основных элементов
  const requiredElements = ['status-message'];
  const missingElements = requiredElements.filter(id => !document.getElementById(id));
  
  if (missingElements.length > 0) {
    console.warn("⚠️ Отсутствуют элементы DOM:", missingElements);
    console.log("🔄 Попытка запуска через 100ms...");
    setTimeout(startApp, 100);
    return;
  }
  
  console.log("✅ DOM готов, запускаем приложение");
  initApp();
}

// Экспорт функций для глобального доступа
window.showStatus = showStatus;
window.isAppInitialized = () => isAppInitialized;

/**
 * Заглушки для недостающих функций (будут реализованы через модули)
 */
function handleWatchAdClick() {
  console.log("handleWatchAdClick: функция будет реализована через модуль ads-manager");
}

function showMainContent() {
  console.log("showMainContent: функция будет реализована через модуль navigation");
}

function showEarnSection() {
  console.log("showEarnSection: функция будет реализована через модуль navigation");
}

function showFriendsSection() {
  console.log("showFriendsSection: функция будет реализована через модуль navigation");
}

function updateAddressPlaceholder() {
  console.log("updateAddressPlaceholder: функция будет реализована через модуль withdrawal");
}

function validateWithdrawalForm() {
  console.log("validateWithdrawalForm: функция будет реализована через модуль withdrawal");
}

function handleRequestWithdrawal() {
  console.log("handleRequestWithdrawal: функция будет реализована через модуль withdrawal");
}

function handleShareAppClick() {
  console.log("handleShareAppClick: функция будет реализована через модуль referral-system");
}

function copyReferralLink() {
  console.log("copyReferralLink: функция будет реализована через модуль referral-system");
}

function recordAdView(adType) {
  console.log("recordAdView: функция будет реализована через модуль api-client");
  return Promise.resolve(true);
}

function startCountdown(button) {
  console.log("startCountdown: функция будет реализована через модуль ads-manager");
}

// Запуск приложения при загрузке DOM
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startApp);
} else {
  startApp();
}
