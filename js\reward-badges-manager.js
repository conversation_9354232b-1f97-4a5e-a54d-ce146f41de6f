/**
 * js/reward-badges-manager.js
 * Менеджер для динамического обновления reward badges из настроек админки
 */

class RewardBadgesManager {
  constructor() {
    this.rewards = {};
    this.isInitialized = false;
    this.apiUrl = 'api/get_ad_rewards.php';
    
    // Маппинг типов рекламы на селекторы reward badges
    this.badgeSelectors = {
      'native_banner': '#openLinkButton .reward-badge',
      'rewarded_video': '#watchVideoButton .reward-badge', 
      'interstitial': '#openAdButton .reward-badge'
    };
    
    console.log('[RewardBadges] 🏆 Менеджер наград инициализирован');
  }

  /**
   * Инициализация - загрузка наград с сервера
   */
  async init() {
    if (this.isInitialized) {
      console.warn('[RewardBadges] Уже инициализирован');
      return;
    }

    try {
      console.log('[RewardBadges] 📡 Загрузка наград с сервера...');
      
      const response = await fetch(this.apiUrl);
      const data = await response.json();
      
      if (data.success) {
        this.rewards = data.rewards;
        console.log('[RewardBadges] ✅ Награды загружены:', this.rewards);
        
        // Обновляем badges в интерфейсе
        this.updateAllBadges();
        
        // Обновляем конфигурацию AdsConfig если доступна
        this.updateAdsConfig();
        
        this.isInitialized = true;
      } else {
        throw new Error(data.error || 'Ошибка загрузки наград');
      }
      
    } catch (error) {
      console.error('[RewardBadges] ❌ Ошибка загрузки наград:', error);

      // БЕЗОПАСНОСТЬ: Никаких fallback наград
      throw new Error('Не удалось загрузить награды с сервера - система заблокирована');
    }
  }

  /**
   * БЕЗОПАСНОСТЬ: Fallback награды удалены
   */
  setFallbackRewards() {
    console.error('[RewardBadges] ❌ Fallback награды отключены для безопасности');
    throw new Error('Fallback награды заблокированы - используйте только серверные данные');
  }

  /**
   * Обновление всех reward badges в интерфейсе
   */
  updateAllBadges() {
    console.log('[RewardBadges] 🔄 Обновление всех reward badges...');
    
    Object.entries(this.badgeSelectors).forEach(([adType, selector]) => {
      this.updateBadge(adType, selector);
    });
  }

  /**
   * Обновление конкретного reward badge
   */
  updateBadge(adType, selector) {
    const element = document.querySelector(selector);
    if (!element) {
      console.warn(`[RewardBadges] ⚠️ Элемент не найден: ${selector}`);
      return;
    }

    const reward = this.rewards[adType];
    if (reward === undefined) {
      console.warn(`[RewardBadges] ⚠️ Награда не найдена для типа: ${adType}`);
      return;
    }

    const newText = `+${reward}`;
    
    // Обновляем только если текст изменился
    if (element.textContent !== newText) {
      element.textContent = newText;
      console.log(`[RewardBadges] ✅ Обновлен badge ${adType}: ${newText}`);
      
      // Добавляем анимацию обновления
      this.animateBadgeUpdate(element);
    }
  }

  /**
   * Анимация обновления badge
   */
  animateBadgeUpdate(element) {
    element.style.transform = 'scale(1.2)';
    element.style.transition = 'transform 0.3s ease';
    
    setTimeout(() => {
      element.style.transform = 'scale(1)';
    }, 300);
  }

  /**
   * Обновление конфигурации AdsConfig с новыми наградами
   */
  updateAdsConfig() {
    if (!window.AdsConfig) {
      console.warn('[RewardBadges] ⚠️ AdsConfig не найден');
      return;
    }

    try {
      // Используем новый метод updateRewards если доступен
      if (typeof window.AdsConfig.updateRewards === 'function') {
        const updated = window.AdsConfig.updateRewards(this.rewards);
        if (updated) {
          console.log('[RewardBadges] ✅ AdsConfig обновлен с новыми наградами через updateRewards()');

          // Уведомляем другие модули об обновлении
          window.dispatchEvent(new CustomEvent('rewardsUpdated', {
            detail: { rewards: this.rewards, source: 'server' }
          }));
        }
      } else {
        // Fallback: обновляем награды напрямую
        Object.entries(this.rewards).forEach(([adType, reward]) => {
          const adTypeConfig = window.AdsConfig.getAdType(adType);
          if (adTypeConfig) {
            adTypeConfig.reward = reward;
          }
        });

        console.log('[RewardBadges] ✅ AdsConfig обновлен с новыми наградами (fallback метод)');
      }
    } catch (error) {
      console.warn('[RewardBadges] ⚠️ Ошибка обновления AdsConfig:', error);
    }
  }

  /**
   * Получение награды для типа рекламы
   */
  getReward(adType) {
    return this.rewards[adType] || 0;
  }

  /**
   * Получение всех наград
   */
  getAllRewards() {
    return { ...this.rewards };
  }

  /**
   * Принудительное обновление наград с сервера
   */
  async refresh() {
    console.log('[RewardBadges] 🔄 Принудительное обновление наград...');
    this.isInitialized = false;
    await this.init();
  }

  /**
   * Обновление конкретной награды
   */
  updateReward(adType, reward) {
    if (this.rewards[adType] !== reward) {
      this.rewards[adType] = reward;
      
      const selector = this.badgeSelectors[adType];
      if (selector) {
        this.updateBadge(adType, selector);
      }
      
      console.log(`[RewardBadges] ✅ Награда ${adType} обновлена: ${reward}`);
    }
  }
}

// Создаем глобальный экземпляр
window.rewardBadgesManager = new RewardBadgesManager();

// Глобальные функции для тестирования
window.updateRewardBadges = () => {
  if (window.rewardBadgesManager) {
    console.log('🏆 Принудительное обновление reward badges...');
    window.rewardBadgesManager.updateAllBadges();
    return true;
  }
  console.warn('❌ RewardBadgesManager не найден');
  return false;
};

window.refreshRewards = async () => {
  if (window.rewardBadgesManager) {
    console.log('🔄 Обновление наград с сервера...');
    await window.rewardBadgesManager.refresh();
    return true;
  }
  console.warn('❌ RewardBadgesManager не найден');
  return false;
};

window.getRewards = () => {
  if (window.rewardBadgesManager) {
    return window.rewardBadgesManager.getAllRewards();
  }
  return {};
};

console.log('[RewardBadges] 🏆 Модуль загружен');
