// === main.js ===
// ПОЛНАЯ ВЕРСИЯ с исправлением инлайнового display:none при анимации v6

// --- Глобальные Константы и Переменные ---
const API_BASE_URL = "./api";
const MY_PUB_ID = "944840";
const MY_APP_ID = "2122";
const DEBUG_MODE = false;
const BOT_USERNAME = "uniqpaid_paid_bot"; // <-- !!! ИМЯ ВАШЕГО БОТА (БЕЗ @) !!!
const PAGE_TRANSITION_DURATION = 300; // мс, для CSS (должно совпадать)
const AUTO_RELOAD_AFTER_COUNTDOWN = true; // 🔄 Автоматический reload после таймера для обновления рекламы
const USE_SOFT_REFRESH = false; // 🔄 Использовать мягкое обновление (переинициализация SDK) вместо полного reload

// Функция для форматирования чисел с двумя знаками после запятой
function formatCurrency(value) {
  return parseFloat(value).toFixed(2);
}

// Ссылки на элементы DOM (все страницы/секции)
const mainContentEl = document.getElementById("main-content");
const earnSectionEl = document.getElementById("earn-section");
const friendsSectionEl = document.getElementById("friends-section");
const allPages = [mainContentEl, earnSectionEl, friendsSectionEl]; // Массив всех страниц

// Остальные элементы
const userNameEl = document.getElementById("user-name");
const balanceAmountEl = document.getElementById("balance-amount");
const headerBalanceInfoEl = document.getElementById("header-balance-info");
const watchAdButton = document.getElementById("openLinkButton");
const watchVideoButton = document.getElementById("watchVideoButton");
const openLinkButton = document.getElementById("openAdButton");
const statusMessageEl = document.getElementById("status-message");
const shareAppButton = document.getElementById("share-app-button");
const referralLinkInput = document.getElementById("referral-link-input");
const copyReferralButton = document.getElementById("copy-referral-button");
const earnBalanceAmountEl = document.getElementById("earn-balance-amount");
const availableWithdrawalEl = document.getElementById("available-withdrawal");
const minWithdrawalEl = document.getElementById("min-withdrawal");
const withdrawalAmountInput = document.getElementById("withdrawal-amount");
const withdrawalAddressInput = document.getElementById("withdrawal-address");
const cryptoCurrencySelect = document.getElementById("crypto-currency");
const requestWithdrawalButton = document.getElementById(
  "request-withdrawal-button"
);
const withdrawalErrorEl = document.getElementById("withdrawal-error");
const navHomeButton = document.getElementById("nav-home");
const navEarnButton = document.getElementById("nav-earn");
const navFriendsButton = document.getElementById("nav-friends");

// Глобальные переменные
const tg = window.Telegram.WebApp;
let adsController = null;
let currentUserId = null;
let currentPageElement = mainContentEl; // Отслеживаем текущую страницу
let isTransitioning = false; // Флаг анимации

// Защищенная переменная баланса с валидацией
let _currentUserBalance = 0;
Object.defineProperty(window, 'currentUserBalance', {
  get: function() {
    return _currentUserBalance;
  },
  set: function(value) {
    // Предупреждение о попытке изменения баланса
    if (typeof value === 'number' && value !== _currentUserBalance) {
      console.warn('🚨 БЕЗОПАСНОСТЬ: Попытка изменения баланса через консоль заблокирована!');
      console.warn('💡 Баланс можно изменить только через серверные операции.');
      return;
    }
    _currentUserBalance = value;
  },
  configurable: false,
  enumerable: true
});

// Настройки вывода средств (получаем с сервера)
let minWithdrawalAmount = 0; // Нет минимальной суммы для вывода
let minBalanceForWithdrawal = 100; // Минимальный баланс для доступа к выводу

// Переменные для отслеживания состояния рекламы
let lastAdShownTime = 0;
let adCooldownTime = 3000; // 3 секунды между показами рекламы
let isAdShowing = false; // Флаг показа рекламы

// Переменные для счетчика обратного отсчета
let countdownTimer = null;
let isButtonPressed = false;
let coinValue = 0.001; // $0.001 за монету (должно соответствовать серверному значению CONVERSION_RATE)

// Настройки приложения (загружаются с сервера)
let appSettings = {
  show_fees_to_user: true,
  conversion_rate: 0.001,
  ad_rewards: {}, // Награды будут загружаться с сервера
  min_balance_for_withdrawal: 100,
  min_withdrawal_amount: 0
};

// Типы рекламы
const AD_TYPES = {
  NATIVE_BANNER: 'native_banner',     // Баннер-превью (для верхней кнопки)
  INTERSTITIAL: 'interstitial',       // Полноэкранный баннер (для средней кнопки)
  REWARDED_VIDEO: 'rewarded_video'    // Видеореклама (для нижней кнопки)
};

// --- Вспомогательные Функции ---

/**
 * Отображает статусное сообщение.
 * @param {string} message Текст сообщения.
 * @param {'info' | 'success' | 'error'} type Тип сообщения.
 */
function showStatus(message, type = "info") {
  if (!statusMessageEl) {
    console.warn("Элемент status-message не найден.");
    return;
  }
  statusMessageEl.textContent = message;
  statusMessageEl.className = "status-message";
  if (type === "success") {
    statusMessageEl.classList.add("success");
  } else if (type === "error") {
    statusMessageEl.classList.add("error");
  }
  console.log(`Status [${type}]: ${message}`);
}

/**
 * Обновляет отображение баланса в нескольких местах.
 * @param {number | string} newBalance Новый баланс.
 */
function updateBalanceDisplay(newBalance) {
  // Безопасное обновление баланса (только для серверных операций)
  _currentUserBalance = parseInt(newBalance) || 0;
  if (balanceAmountEl) balanceAmountEl.textContent = _currentUserBalance;
  if (earnBalanceAmountEl) earnBalanceAmountEl.textContent = _currentUserBalance;
  if (availableWithdrawalEl) {
    availableWithdrawalEl.textContent = _currentUserBalance;
    console.log(`💰 Элемент "Доступно для вывода" обновлен: ${_currentUserBalance} монет`);
  } else {
    console.warn('⚠️ Элемент "available-withdrawal" не найден!');
  }

  // Обновляем баланс в калькуляторе
  const calcBalance = document.getElementById('calc-balance');
  if (calcBalance) {
    calcBalance.textContent = `${_currentUserBalance} монет`;
  }

  // Обновляем расчеты в калькуляторе, если есть введенная сумма
  const calcAmountInput = document.getElementById('calc-amount');
  if (calcAmountInput && calcAmountInput.value) {
    const amount = parseInt(calcAmountInput.value) || 0;
    updateCalculatorDisplay(amount);
    updateBalanceCheck(amount);
  }

  // Повторно валидируем основную форму вывода, так как изменение баланса может повлиять на ее валидность
  validateWithdrawalForm();

  console.log(`🎯 Баланс обновлен: ${_currentUserBalance}`);
}

// --- Функции Переключения Видов с АНИМАЦИЕЙ (Исправленная Логика v6) ---

/**
 * Переключает видимую страницу с CSS-анимацией (v6 - сброс inline display).
 * @param {HTMLElement} nextPageElement Элемент новой страницы.
 * @param {HTMLElement} [activeNavButton] Кнопка навигации для активации.
 */
function switchPageAnimated(nextPageElement, activeNavButton) {
  if (
    !nextPageElement ||
    nextPageElement === currentPageElement ||
    isTransitioning
  ) {
    console.log(
      `Переключение отменено: next=${nextPageElement?.id}, current=${currentPageElement?.id}, transitioning=${isTransitioning}`
    );
    return;
  }
  console.log(
    `Анимация v6: ${currentPageElement?.id} -> ${nextPageElement.id}`
  );
  isTransitioning = true;
  const pageOutElement = currentPageElement;

  // 1. Обновляем кнопку навигации
  updateActiveNavButton(activeNavButton);

  // --- Подготовка новой страницы ---
  // СНАЧАЛА УБИРАЕМ КЛАСС СКРЫТИЯ И СБРАСЫВАЕМ ИНЛАЙНОВЫЙ DISPLAY
  nextPageElement.classList.remove("page-hidden");
  nextPageElement.style.display = ""; // <-- ВАЖНО: Сбрасываем инлайновый стиль!
  // Сбрасываем классы анимации на всякий случай
  nextPageElement.classList.remove(
    "page-leave-active",
    "page-enter-active",
    "page-enter"
  );
  // Ставим начальное состояние анимации входа
  nextPageElement.classList.add("page-enter");
  console.log(
    `Новая страница ${nextPageElement.id}: убран hidden, сброшен inline display, добавлен enter`
  );

  // --- Анимация ухода старой страницы ---
  if (pageOutElement) {
    pageOutElement.classList.remove("page-enter-active");
    pageOutElement.classList.add("page-leave-active"); // Запускаем анимацию ухода
    console.log(`Старая страница ${pageOutElement.id}: добавлен leave-active`);
  }

  // --- Запуск анимации входа новой страницы (после reflow) ---
  requestAnimationFrame(() => {
    nextPageElement.classList.remove("page-enter");
    nextPageElement.classList.add("page-enter-active");
    console.log(
      `Новая страница ${nextPageElement.id}: убран enter, добавлен enter-active`
    );
  });

  // 5. Обновляем текущую страницу СРАЗУ
  currentPageElement = nextPageElement;

  // 6. Завершение анимации и очистка классов
  setTimeout(() => {
    // Убираем классы анимации с НОВОЙ (теперь текущей) страницы
    currentPageElement.classList.remove("page-enter-active");
    console.log(`Новая страница ${currentPageElement.id}: убран enter-active`);

    // Прячем СТАРУЮ страницу (добавляем класс) и убираем класс анимации
    if (pageOutElement) {
      pageOutElement.classList.add("page-hidden"); // Добавляем класс для скрытия
      pageOutElement.classList.remove("page-leave-active");
      // Дополнительно можно сбросить инлайн стиль и у старой страницы
      pageOutElement.style.display = "";
      console.log(
        `Старая страница ${pageOutElement.id}: добавлен hidden, убран leave-active, сброшен inline display`
      );
    }

    isTransitioning = false; // Разрешаем следующее переключение
    console.log(`Анимация v6 завершена. Активна: ${currentPageElement.id}`);

    // Действия после переключения
    if (currentPageElement === friendsSectionEl) {
      generateReferralLink();
      loadReferralStats(); // Загружаем статистику рефералов при переходе на страницу друзей
    }
    if (currentPageElement === earnSectionEl) {
      updateWithdrawalSection();
      // Инициализируем подсказку для рекомендаций к выводу
      initWithdrawalRecommendationsTooltip();
    }
  }, PAGE_TRANSITION_DURATION); // Ждем время анимации
}

/** Показывает главную секцию */
function showMainContent() {
  switchPageAnimated(mainContentEl, navHomeButton);
}
/** Показывает секцию заработка */
function showEarnSection() {
  switchPageAnimated(earnSectionEl, navEarnButton);
  updateWithdrawalSection();
  initWithdrawalRecommendationsTooltip();

  // ПРИНУДИТЕЛЬНАЯ ЗАГРУЗКА ИСТОРИИ ВЫПЛАТ
  console.log('[Earn Section] Принудительная загрузка истории выплат');

  // Загружаем историю сразу
  loadAndDisplayWithdrawalHistory();

  // Затем проверяем и обновляем статусы
  checkAndUpdateWithdrawalStatuses().then(() => {
    console.log('[Earn Section] Статусы обновлены, перезагружаем историю');
    loadAndDisplayWithdrawalHistory();
  }).catch((error) => {
    console.log('[Earn Section] Ошибка обновления статусов:', error);
    // Если проверка статусов не удалась, все равно загружаем историю
    loadAndDisplayWithdrawalHistory();
  });
}
/** Показывает секцию друзей */
function showFriendsSection() {
  switchPageAnimated(friendsSectionEl, navFriendsButton);
}

/** Обновляет активное состояние кнопок навигации */
function updateActiveNavButton(activeButton) {
  [navHomeButton, navEarnButton, navFriendsButton].forEach((button) => {
    if (button) button.classList.remove("active");
  });
  if (activeButton) {
    activeButton.classList.add("active");
  }
}

// --- Функции Взаимодействия с Бэкендом (PHP API) ---

/**
 * Загружает настройки приложения с сервера
 */
async function loadAppSettings() {
  try {
    console.log('[Settings] Загрузка настроек приложения...');

    const response = await fetch(`${API_BASE_URL}/getAppSettings.php`, {
      method: "GET",
      headers: { "Content-Type": "application/json" }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.settings) {
        appSettings = { ...appSettings, ...data.settings };
        console.log('[Settings] Настройки загружены:', appSettings);

        // Обновляем coinValue из настроек
        coinValue = appSettings.conversion_rate;

        // Загружаем актуальные данные о валютах
        await loadActualCurrencyData();

        // Обновляем отображение комиссий и минимумов
        updateFeeDisplay();
        updateMinimumDisplay();

        // Принудительно обновляем калькулятор с новыми минимумами
        const calcAmountInput = document.getElementById('calc-amount');
        if (calcAmountInput) {
          const currentAmount = parseInt(calcAmountInput.value) || 0;
          updateCalculatorDisplay(currentAmount);
        }

        return true;
      }
    }

    console.warn('[Settings] Не удалось загрузить настройки, используем значения по умолчанию');
    return false;

  } catch (error) {
    console.warn('[Settings] Ошибка загрузки настроек:', error);
    return false;
  }
}

/**
 * Загружает актуальные данные о валютах с сервера
 */
async function loadActualCurrencyData() {
  try {
    console.log('[Currency] Загрузка актуальных данных о валютах...');

    // ИСПРАВЛЕНИЕ: Используем кэшированные данные для быстрой загрузки
    const response = await fetch(`${API_BASE_URL}/getCachedCurrencyData.php`, {
      method: "GET",
      headers: { "Content-Type": "application/json" }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.currencies) {
        // Обновляем данные о валютах
        Object.assign(currencyData, data.currencies);

        // Сохраняем полные данные включая курсы для использования в расчетах
        window.loadedCurrencyData = data;

        console.log('[Currency] Актуальные данные о валютах загружены:', currencyData);
        console.log('[Currency] Курсы валют:', data.exchange_rates);

        // Принудительно обновляем отображение минимумов и комиссий
        updateMinimumDisplay();
        updateFeeDisplay();

        // Обновляем калькулятор если есть введенная сумма
        const calcAmountInput = document.getElementById('calc-amount');
        if (calcAmountInput && calcAmountInput.value) {
          const currentAmount = parseInt(calcAmountInput.value) || 0;
          if (currentAmount > 0) {
            updateCalculatorDisplay(currentAmount);
          }
        }

        return true;
      }
    }

    console.warn('[Currency] Не удалось загрузить данные о валютах, используем значения по умолчанию');
    return false;

  } catch (error) {
    console.warn('[Currency] Ошибка загрузки данных о валютах:', error);
    return false;
  }
}

/**
 * Обновляет отображение комиссий в интерфейсе
 */
function updateFeeDisplay() {
  console.log('[UI] Обновление отображения комиссий...');

  // Обновляем отображение комиссий в калькуляторе
  const feeDisplays = document.querySelectorAll('.fee-amount, .network-fee');
  feeDisplays.forEach(element => {
    const currency = element.dataset.currency;
    if (currency && currencyData[currency]) {
      if (appSettings.show_fees_to_user) {
        element.textContent = `$${currencyData[currency].networkFee}`;
        element.style.display = 'inline';
      } else {
        element.textContent = 'Скрыто';
        element.style.display = 'inline';
        element.style.opacity = '0.6';
      }
    }
  });

  // Обновляем подсказки о комиссиях
  const feeHints = document.querySelectorAll('.fee-hint');
  feeHints.forEach(hint => {
    if (appSettings.show_fees_to_user) {
      hint.style.display = 'block';
    } else {
      hint.style.display = 'none';
    }
  });
}

/**
 * Обновляет отображение минимумов в интерфейсе
 */
function updateMinimumDisplay() {
  console.log('[UI] Обновление отображения минимумов...');

  // Обновляем минимумы в карточках валют
  const currencyCards = document.querySelectorAll('.currency-card');
  currencyCards.forEach(card => {
    const currency = card.dataset.currency;
    if (currency && currencyData[currency]) {
      const minElement = card.querySelector('.min-amount');
      if (minElement) {
        const minCoins = currencyData[currency].minCoins;
        const minUsd = (minCoins * coinValue).toFixed(2);
        const coinsText = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        minElement.textContent = `${minCoins.toLocaleString()} ${coinsText} ($${minUsd})`;
        console.log(`[UI] Обновлен минимум для ${currency}: ${minCoins} монет`);
      }
    }
  });

  // Обновляем минимумы в селекте валют
  const cryptoCurrencySelect = document.getElementById('crypto-currency');
  if (cryptoCurrencySelect) {
    Array.from(cryptoCurrencySelect.options).forEach(option => {
      const currency = option.value;
      if (currency && currencyData[currency]) {
        const minCoins = currencyData[currency].minCoins;
        const originalText = option.textContent.split(' (мин:')[0];
        const coinsText = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        option.textContent = `${originalText} (мин: ${minCoins.toLocaleString()} ${coinsText})`;
      }
    });
  }

  // Обновляем минимумы в информационной карточке валюты
  updateCurrencyInfoCard();
}

/**
 * Обновляет информационную карточку валюты с актуальными минимумами
 */
function updateCurrencyInfoCard() {
  const activeTab = document.querySelector('.currency-tab.active');
  if (!activeTab) return;

  const currency = activeTab.dataset.currency;
  const data = currencyData[currency];
  if (!data) return;

  // ИСПРАВЛЕНИЕ: Обновляем минимум в карточке валюты (ТОЛЬКО первый элемент - индекс 0)
  const requirementValues = document.querySelectorAll('.requirement-value');
  if (requirementValues.length >= 1) {
    const minCoins = data.minCoins;
    const minUsd = (minCoins * coinValue).toFixed(2);
    // Обновляем только элемент с индексом 0 (Минимум к выводу) с переносом строки
    const coinsText = window.appLocalization ?
      window.appLocalization.get('currency.coins') :
      'монет';
    requirementValues[0].innerHTML = `${minCoins.toLocaleString()} ${coinsText}<br>($${minUsd})`;
    console.log(`[UI] Обновлен минимум в карточке для ${currency}: ${minCoins} монет`);
  }

  // ИСПРАВЛЕНИЕ: Обновляем комиссию в карточке валюты (ТОЛЬКО элемент с индексом 2)
  if (requirementValues.length >= 3) {
    if (data.networkFee) {
      if (appSettings && appSettings.show_fees_to_user) {
        // Обновляем элемент с индексом 2 (Сетевая комиссия)
        requirementValues[2].textContent = `$${data.networkFee}`;
        requirementValues[2].style.opacity = '1';
      } else {
        requirementValues[2].textContent = 'Скрыто';
        requirementValues[2].style.opacity = '0.6';
      }
    }
  }

  // 🔧 ПРИНУДИТЕЛЬНОЕ ИСПРАВЛЕНИЕ ЛЕЙБЛОВ при каждом обновлении карточки
  setTimeout(() => {
    const requirementLabels = document.querySelectorAll('.requirement-label');
    const correctLabels = [
      'Минимум к выводу:',
      'Сумма к выводу:',
      'Сетевая комиссия:',
      'Вы получите:',
      'Эффективность:'
    ];

    requirementLabels.forEach((label, index) => {
      if (index < correctLabels.length && label.textContent !== correctLabels[index]) {
        label.textContent = correctLabels[index];
        console.log(`🔧 Исправлен лейбл ${index}: "${correctLabels[index]}"`);
      }
    });
  }, 100); // Небольшая задержка для применения изменений
}

/**
 * Применяет локализацию на основе языка пользователя
 */
async function applyUserLocalization() {
  try {
    console.log('[Localization] Применение локализации пользователя...');

    // Получаем язык пользователя с сервера
    const response = await fetch(`${API_BASE_URL}/getUserLanguage.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.language) {
        console.log(`[Localization] Язык пользователя с сервера: ${data.language}`);

        // Устанавливаем язык в локализации
        if (window.appLocalization) {
          window.appLocalization.setLanguage(data.language);
          console.log(`[Localization] Язык установлен: ${data.language}`);

          // Принудительно применяем переводы
          window.appLocalization.applyTranslations();
          console.log('[Localization] Переводы применены к интерфейсу');
        }
      }
    } else {
      console.warn('[Localization] Не удалось получить язык пользователя с сервера');

      // Fallback: используем локальное определение языка
      if (window.appLocalization) {
        window.appLocalization.detectLanguage();
        window.appLocalization.applyTranslations();
        console.log('[Localization] Использовано локальное определение языка');
      }
    }
  } catch (error) {
    console.warn('[Localization] Ошибка применения локализации:', error);

    // Fallback: используем локальное определение языка
    if (window.appLocalization) {
      window.appLocalization.detectLanguage();
      window.appLocalization.applyTranslations();
      console.log('[Localization] Использован fallback на локальное определение');
    }
  }
}

/** Запрашивает данные пользователя с сервера. */
async function fetchUserData() {
  const userData = getUserDataForAPI();
  if (!userData) {
    showStatus("Ошибка: Нет данных пользователя.", "error");
    tg.showAlert("Не удалось получить данные пользователя.");
    if (watchAdButton) watchAdButton.disabled = true;
    return;
  }
  showStatus("Загрузка данных...");
  try {
    const response = await fetch(`${API_BASE_URL}/getUserData.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(userData),
    });
    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }
    const data = await response.json();
    if (data.error) throw new Error(data.error);
    currentUserId = data.userId;

    // Обновляем настройки вывода средств с сервера
    if (data.min_withdrawal !== undefined) minWithdrawalAmount = data.min_withdrawal;
    if (data.min_balance_for_withdrawal !== undefined) minBalanceForWithdrawal = data.min_balance_for_withdrawal;

    // Обновляем отображение минимальной суммы в HTML
    if (minWithdrawalEl) {
      if (minWithdrawalAmount > 0) {
        minWithdrawalEl.textContent = minWithdrawalAmount;
      } else {
        // Если нет минимальной суммы, скрываем или обновляем текст
        const parentHint = minWithdrawalEl.closest('.hint');
        if (parentHint) {
          parentHint.style.display = 'none';
        }
      }
    }

    if (userNameEl) {
      const u = tg.initDataUnsafe?.user;
      if (u?.username) userNameEl.textContent = `@${u.username}`;
      else if (u?.first_name)
        userNameEl.textContent =
          u.first_name + (u.last_name ? ` ${u.last_name}` : "");
      else userNameEl.textContent = `User ${currentUserId || "???"}`;
    }
    updateBalanceDisplay(data.balance);

    // 🌍 ПРИМЕНЯЕМ ЛОКАЛИЗАЦИЮ после загрузки данных пользователя
    await applyUserLocalization();

    showStatus("Данные загружены.", "success");
    setTimeout(() => {
      if (statusMessageEl.textContent === "Данные загружены.") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[Fetch User Data] Ошибка:", error);
    showStatus(`${error.message}`, "error");
    tg.showAlert(`${error.message}`);
    if (watchAdButton) watchAdButton.disabled = true;
  }
}

/** Отправляет запрос на сервер для записи просмотра рекламы. */
async function recordAdView(adType = 'default') {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    tg.showAlert("Критическая ошибка: Невозможно засчитать награду.");
    return false;
  }

  console.log(`[API] Запись просмотра рекламы типа: ${adType}`);
  showStatus("Запись просмотра...");

  try {
    // Определяем награду из настроек приложения
    const reward = appSettings.ad_rewards[adType] || 1;

    const response = await fetch(`${API_BASE_URL}/recordAdView.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        adType: adType,
        reward: reward // Явно передаем награду на сервер
      }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();
    if (data.error) throw new Error(data.error);

    updateBalanceDisplay(data.newBalance);

    // 🎵 ВОСПРОИЗВОДИМ ЗВУК МОНЕТ ПРИ УСПЕШНОМ ПРОМИСЕ RICHADDS!
    console.log('[ЗВУК] Проверяем возможность воспроизведения звука...');
    if (window.playCoinsSound) {
      // Определяем количество монет для звука на основе типа рекламы
      const reward = appSettings.ad_rewards[adType] || 10;
      console.log(`[ЗВУК] Воспроизводим звук для ${reward} монет (тип: ${adType})`);
      window.playCoinsSound(reward);
    } else {
      console.warn('[ЗВУК] window.playCoinsSound не найден!');
    }

    showStatus(`Награда зачислена! Баланс: ${data.newBalance}`, "success");
    tg.HapticFeedback.notificationOccurred("success");

    setTimeout(() => {
      if (statusMessageEl.textContent.startsWith("Награда зачислена"))
        showStatus("");
    }, 2500);

    return true;
  } catch (error) {
    console.error("[Record Ad View] Ошибка:", error);
    showStatus(`Ошибка записи: ${error.message}`, "error");
    tg.showAlert(`Не удалось засчитать награду: ${error.message}`);
    tg.HapticFeedback.notificationOccurred("error");
    return false;
  }
}

// --- Функции для расчета криптовалюты ---

/**
 * Рассчитывает сумму в криптовалюте с учетом комиссии и баланса
 * @param {number} coinAmount - Количество монет
 * @param {string} currency - Валюта
 * @param {boolean} checkBalance - Проверять ли баланс пользователя (по умолчанию true)
 * @returns {Promise<object>} - Объект с результатом расчета
 */
async function calculateCryptoAmount(coinAmount, currency, checkBalance = true) {
  if (!coinAmount || coinAmount <= 0) {
    return { amount: "0", status: "empty", message: "" };
  }

  // Проверяем баланс пользователя
  const userBalance = currentUserBalance || 0;
  if (checkBalance && coinAmount > userBalance) {
    const needed = coinAmount - userBalance;
    return {
      amount: "0",
      status: "insufficient_balance",
      message: `Недостаточно средств! Нужно: ${needed} монет`
    };
  }

  // Получаем данные о валюте из калькулятора
  const currencyInfo = currencyData[currency];
  if (!currencyInfo) {
    return { amount: "0", status: "error", message: "Валюта не поддерживается" };
  }

  // Проверяем минимальную сумму для валюты
  if (coinAmount < currencyInfo.minCoins) {
    const minCoinsFormatted = currencyInfo.minCoins.toLocaleString();
    const minUsd = (currencyInfo.minCoins * 0.001).toFixed(2);
    return {
      amount: "0",
      status: "insufficient_minimum",
      message: `Минимум: ${minCoinsFormatted} монет ($${minUsd})`
    };
  }

  try {
    // Используем серверный API для точного расчета (синхронизировано с requestWithdrawal.php)
    const response = await fetch(`${API_BASE_URL}/calculateWithdrawalAmount.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        coins_amount: coinAmount,
        currency: currency
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
      // Обработка различных типов ошибок из единого калькулятора
      if (data.error_code === 'FEE_EXCEEDS_AMOUNT') {
        return {
          amount: "0",
          status: "fee_exceeds_amount",
          message: "Комиссия больше суммы"
        };
      }

      if (data.error_code === 'AMOUNT_TOO_LOW') {
        return {
          amount: "0",
          status: "insufficient_minimum",
          message: `Минимум: ${data.minimum_required || 'неизвестно'}`
        };
      }

      // Обратная совместимость со старыми ошибками
      if (data.error === 'Комиссия больше суммы вывода' || data.error === 'Комиссия NOWPayments больше суммы вывода') {
        return {
          amount: "0",
          status: "fee_exceeds_amount",
          message: "Комиссия больше суммы"
        };
      }

      // ИСПРАВЛЕНИЕ: Вместо throw используем fallback
      console.warn(`Переходим на fallback расчет из-за ошибки API: ${data.error}`);
      return calculateCryptoAmountFallback(coinAmount, currency);
    }

    // ИСПРАВЛЕНИЕ: Все криптовалюты округляем до 8 знаков после запятой для точности
    let formattedAmount;
    const cryptoAmount = parseFloat(data.crypto_amount); // Убедимся, что это число
    formattedAmount = cryptoAmount.toFixed(8); // Применяем toFixed(8) для всех

    console.log(`Серверный расчет для ${currency}: ${coinAmount} монет = ${formattedAmount} ${currency.toUpperCase()} (метод: ${data.calculation_method || 'unified_fee_calculator'})`);

    return {
      amount: formattedAmount,
      status: "success",
      message: "",
      raw_amount: cryptoAmount,
      usd_amount: data.usd_amount,
      nowpayments_fee: data.nowpayments_fee,
      crypto_amount_gross: data.crypto_amount_gross,
      fee_details: data.fee_details,
      calculation_method: data.calculation_method || 'unified_fee_calculator'
    };

  } catch (error) {
    console.warn(`Ошибка серверного расчета для ${currency}:`, error);

    // Fallback на локальный расчет
    return calculateCryptoAmountFallback(coinAmount, currency);
  }
}

/**
 * Fallback функция для локального расчета (если сервер недоступен)
 */
function calculateCryptoAmountFallback(coinAmount, currency) {
  console.warn(`Используем fallback расчет для ${currency} (БЕЗ предварительного вычета комиссии)`);

  const dollarAmount = coinAmount * coinValue;
  const currencyInfo = currencyData[currency];

  // НОВАЯ ЛОГИКА: НЕ вычитаем комиссию в fallback режиме
  // Комиссия будет обработана NOWPayments автоматически
  const afterFee = dollarAmount;

  // ОБНОВЛЕННЫЕ курсы (синхронизированы с сервером, актуальные на 18.06.2025)
  const fallbackRates = {
    'eth': 2502,      // ETH ~$2502 (обновлено)
    'btc': 104620,    // BTC ~$104620 (обновлено)
    'ton': 2.9,       // TON ~$2.9 (обновлено)
    'usdttrc20': 1.0, // USDT ~$1
    'ltc': 86,        // LTC ~$86
    'bch': 402,       // BCH ~$402
    'xrp': 0.65,      // XRP ~$0.65
    'ada': 0.65,      // ADA ~$0.65
    'dot': 10         // DOT ~$10
  };

  const rate = fallbackRates[currency];
  if (!rate) {
    console.error(`Неподдерживаемая валюта в fallback: ${currency}`);
    return {
      amount: "0",
      status: "error",
      message: "Валюта не поддерживается"
    };
  }

  // Рассчитываем сумму в криптовалюте
  const cryptoAmountGross = afterFee / rate;

  // Примерная комиссия для fallback (будет заменена реальной комиссией NOWPayments)
  const estimatedFee = cryptoAmountGross * 0.02; // 2% примерная комиссия
  const cryptoAmountNet = Math.max(0, cryptoAmountGross - estimatedFee);

  // ИСПРАВЛЕНИЕ: Все криптовалюты округляем до 8 знаков после запятой
  let formattedAmount;
  if (currency === 'btc') {
    formattedAmount = cryptoAmountNet.toFixed(8);
  } else if (currency === 'eth') {
    formattedAmount = cryptoAmountNet.toFixed(8); // Было 6, стало 8
  } else if (currency === 'ton') {
    formattedAmount = cryptoAmountNet.toFixed(8); // Было 4, стало 8
  } else if (currency === 'usdttrc20') {
    formattedAmount = cryptoAmountNet.toFixed(8); // Было 4, стало 8
  } else {
    formattedAmount = cryptoAmountNet.toFixed(8); // Все остальные тоже 8 знаков
  }

  return {
    amount: formattedAmount,
    status: "success",
    message: "",
    fee_calculation_method: "fallback_estimate"
  };
}

/**
 * Обновляет поле суммы в криптовалюте с учетом баланса
 */
async function updateCryptoAmountField() {
  // Получаем элементы каждый раз для надежности
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');
  const cryptoCurrencySelect = document.getElementById('crypto-currency');
  const cryptoAmountField = document.getElementById('crypto-amount');

  if (!cryptoAmountField) return;

  // Получаем сумму из поля вывода или из калькулятора
  let withdrawalAmount = parseFloat(withdrawalAmountInput?.value) || 0;

  // Если поле вывода пустое, берем значение из калькулятора
  if (withdrawalAmount === 0) {
    const calcAmountInput = document.getElementById('calc-amount');
    withdrawalAmount = parseFloat(calcAmountInput?.value) || 0;
  }

  // Получаем выбранную валюту из селекта или из активного таба калькулятора
  let selectedCurrency = cryptoCurrencySelect?.value;

  if (!selectedCurrency) {
    const activeTab = document.querySelector('.currency-tab.active');
    selectedCurrency = activeTab?.dataset.currency || 'ton';
  }

  if (withdrawalAmount > 0) {
    // Показываем индикатор загрузки
    cryptoAmountField.value = '⏳ Расчет...';
    cryptoAmountField.style.color = 'var(--cyber-text-secondary, #888)';

    try {
      // ИСПРАВЛЕНИЕ: Используем тот же серверный расчет что и калькулятор
      const result = await calculateCryptoAmount(withdrawalAmount, selectedCurrency, false);

      // Проверяем баланс пользователя
      const userBalance = currentUserBalance || 0;
      if (withdrawalAmount > userBalance) {
        const needed = withdrawalAmount - userBalance;
        cryptoAmountField.value = `❌ Недостаточно средств! Нужно: ${needed} монет`;
        cryptoAmountField.style.color = 'var(--cyber-error-color, #ff6b6b)';
        cryptoAmountField.title = `Недостаточно средств! Нужно: ${needed} монет`;
        return;
      }

      const currencyName = cryptoCurrencySelect?.options[cryptoCurrencySelect.selectedIndex]?.text ||
                          currencyData[selectedCurrency]?.name || 'USDT';
      const shortName = currencyName.split(' ')[0];

      // ИСПРАВЛЕНИЕ: Обновляем поле в зависимости от статуса серверного расчета
      switch (result.status) {
        case "success":
          // ИСПРАВЛЕНИЕ: Используем сумму ПОСЛЕ вычета комиссии из нового API
          const finalCryptoAmount = result.raw_amount || result.amount; // Сумма после вычета комиссии
          const grossCryptoAmount = result.crypto_amount_gross || finalCryptoAmount; // Сумма до вычета комиссии
          const feeAmount = result.nowpayments_fee || 0; // Комиссия в криптовалюте

          // ИСПРАВЛЕНИЕ: Округляем до 8 знаков после запятой для точности
          const formattedAmount = parseFloat(finalCryptoAmount).toFixed(8);

          // Показываем сумму ПОСЛЕ вычета комиссии (то что пользователь реально получит)
          cryptoAmountField.value = `${formattedAmount} ${shortName}`;
          cryptoAmountField.style.color = 'var(--cyber-text-primary)';

          // Добавляем информацию о комиссии в tooltip
          if (feeAmount > 0) {
            cryptoAmountField.title = `Вы получите: ${finalCryptoAmount} ${shortName}\nДо комиссии: ${grossCryptoAmount} ${shortName}\nКомиссия: ${feeAmount} ${shortName}\nМетод: ${result.calculation_method || 'unified_fee_calculator'}`;
          } else {
            cryptoAmountField.title = `Метод расчета: ${result.calculation_method || 'unified_fee_calculator'}`;
          }

          console.log(`[updateCryptoAmountField] ${shortName}: Показываем ${finalCryptoAmount} (после комиссии) вместо ${grossCryptoAmount} (до комиссии)`);
          break;

        case "insufficient_minimum":
          cryptoAmountField.value = `⚠️ ${result.message}`;
          cryptoAmountField.style.color = 'var(--cyber-warning-color, #ffa726)';
          cryptoAmountField.title = result.message;
          break;

        case "fee_exceeds_amount":
          cryptoAmountField.value = `💸 ${result.message}`;
          cryptoAmountField.style.color = 'var(--cyber-error-color, #ff6b6b)';
          cryptoAmountField.title = result.message;
          break;

        default:
          cryptoAmountField.value = '❓ Ошибка расчета';
          cryptoAmountField.style.color = 'var(--cyber-error-color, #ff6b6b)';
          cryptoAmountField.title = 'Неизвестная ошибка';
      }
    } catch (error) {
      console.error('Ошибка при расчете суммы криптовалюты:', error);
      cryptoAmountField.value = '❌ Ошибка расчета';
      cryptoAmountField.style.color = 'var(--cyber-error-color, #ff6b6b)';
      cryptoAmountField.title = 'Ошибка при расчете суммы';
    }
  } else {
    cryptoAmountField.value = '';
    cryptoAmountField.style.color = 'var(--cyber-text-primary)';
    cryptoAmountField.placeholder = 'Будет рассчитано автоматически';
    cryptoAmountField.title = '';
  }
}

// --- Функции для счетчика обратного отсчета ---

/**
 * Запускает счетчик обратного отсчета на кнопке
 * @param {HTMLElement} button - Кнопка для блокировки
 * @param {number} seconds - Количество секунд для отсчета (по умолчанию 20)
 */
function startCountdown(button, seconds = 20) {
  if (!button || isButtonPressed) return;

  isButtonPressed = true;

  // Добавляем класс нажатой кнопки
  button.classList.add('pressed');

  // Блокируем конкретную кнопку с таймером
  button.disabled = true;
  button.style.pointerEvents = 'none'; // Дополнительная защита от кликов

  // Блокируем все кнопки рекламы
  disableAllAdButtons();

  // Создаем элемент счетчика
  const countdownOverlay = document.createElement('div');
  countdownOverlay.className = 'countdown-overlay';

  // Создаем элемент для цифр с улучшенной контрастностью
  const countdownTime = document.createElement('span');
  countdownTime.className = 'countdown-time';
  countdownTime.textContent = seconds;
  countdownOverlay.appendChild(countdownTime);

  button.appendChild(countdownOverlay);

  let remainingTime = seconds;

  countdownTimer = setInterval(() => {
    remainingTime--;
    countdownTime.textContent = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;

      // Убираем счетчик и возвращаем кнопку в исходное состояние
      button.removeChild(countdownOverlay);
      button.classList.remove('pressed');

      // Разблокируем конкретную кнопку с таймером
      button.disabled = false;
      button.style.pointerEvents = 'auto'; // Восстанавливаем возможность кликов

      // Разблокируем все кнопки рекламы
      enableAllAdButtons();

      isButtonPressed = false;

      // 🔄 АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ РЕКЛАМНЫХ БЛОКОВ
      if (AUTO_RELOAD_AFTER_COUNTDOWN) {
        if (USE_SOFT_REFRESH) {
          // Мягкое обновление - переинициализация SDK без reload страницы
          console.log("[Countdown] Таймер завершен, выполняем мягкое обновление рекламы");
          setTimeout(() => {
            refreshAdBlocks();
          }, 500);
        } else {
          // Полный reload страницы
          console.log("[Countdown] Таймер завершен, выполняем reload страницы для обновления рекламы");

          // Показываем уведомление о reload
          showStatus("Обновляем рекламные блоки...", "info");

          // Небольшая задержка перед reload для показа сообщения
          setTimeout(() => {
            try {
              // Сохраняем текущую страницу в localStorage перед reload
              if (currentPageElement) {
                localStorage.setItem('lastActivePage', currentPageElement.id);
              }

              // Выполняем reload страницы
              window.location.reload();
            } catch (error) {
              console.warn("[Countdown] Ошибка при reload:", error);
              // Если reload не удался, пробуем мягкое обновление
              refreshAdBlocks();
            }
          }, 1000);
        }
      } else {
        console.log("[Countdown] Таймер завершен, автоматическое обновление отключено");
        // Просто показываем сообщение о том, что можно смотреть рекламу снова
        showStatus("Можете смотреть рекламу снова!", "success");
        setTimeout(() => {
          if (statusMessageEl.textContent === "Можете смотреть рекламу снова!") {
            showStatus("", "");
          }
        }, 3000);
      }
    }
  }, 1000);
}

/**
 * Блокирует все кнопки рекламы
 */
function disableAllAdButtons() {
  if (watchAdButton) {
    watchAdButton.disabled = true;
    watchAdButton.style.pointerEvents = 'none';
  }
  if (watchVideoButton) {
    watchVideoButton.disabled = true;
    watchVideoButton.style.pointerEvents = 'none';
  }
  if (openLinkButton) {
    openLinkButton.disabled = true;
    openLinkButton.style.pointerEvents = 'none';
  }
}

/**
 * Разблокирует все кнопки рекламы
 */
function enableAllAdButtons() {
  if (watchAdButton) {
    watchAdButton.disabled = false;
    watchAdButton.style.pointerEvents = 'auto';
  }
  if (watchVideoButton) {
    watchVideoButton.disabled = false;
    watchVideoButton.style.pointerEvents = 'auto';
  }
  if (openLinkButton) {
    openLinkButton.disabled = false;
    openLinkButton.style.pointerEvents = 'auto';
  }
}

/**
 * 🔄 Обновляет рекламные блоки без полного reload страницы
 */
function refreshAdBlocks() {
  console.log("[Ad Refresh] Попытка обновления рекламных блоков...");

  try {
    // Показываем статус обновления
    showStatus("Обновляем рекламу...", "info");

    // Если есть активный контроллер рекламы, пробуем его переинициализировать
    if (adsController && typeof adsController.initialize === 'function') {
      console.log("[Ad Refresh] Переинициализация RichAds SDK...");

      adsController.initialize({
        pubId: MY_PUB_ID,
        appId: MY_APP_ID,
        debug: DEBUG_MODE,
      });

      setTimeout(() => {
        showStatus("Реклама обновлена!", "success");
        setTimeout(() => {
          if (statusMessageEl.textContent === "Реклама обновлена!") {
            showStatus("", "");
          }
        }, 2000);
      }, 1000);

    } else {
      // Если контроллер недоступен, пробуем полную переинициализацию
      console.log("[Ad Refresh] Полная переинициализация рекламы...");
      initializeRichAds();
    }

  } catch (error) {
    console.warn("[Ad Refresh] Ошибка обновления рекламы:", error);
    showStatus("Ошибка обновления рекламы", "error");
    setTimeout(() => {
      showStatus("", "");
    }, 3000);
  }
}

/**
 * Показывает сообщение об отсутствии рекламы
 */
function showNoAdMessage() {
  const message = window.appLocalization ? window.appLocalization.get('ads.no_ads_available') : "Нет доступной рекламы. Попробуйте позже.";
  showStatus(message, "info");
  setTimeout(() => {
    if (statusMessageEl.textContent === message) {
      showStatus("");
    }
  }, 3000);
}

// --- Функции Работы с RichAds SDK ---

/** Инициализирует RichAds SDK. */
function initializeRichAds() {
  showStatus("Инициализация рекламы...");
  try {
    console.log("[RichAds Init] Проверка доступности SDK...");

    // Проверяем наличие SDK
    if (typeof TelegramAdsController !== "function" && typeof TelegramAdsController !== "object") {
      console.warn("[RichAds Init] SDK не найден, пробуем загрузить скрипт вручную");

      // Пробуем загрузить скрипт вручную
      const script = document.createElement('script');
      script.src = "https://richinfo.co/richpartners/telegram/js/tg-ob.js";
      document.head.appendChild(script);

      // Устанавливаем таймаут для повторной попытки
      setTimeout(() => {
        console.log("[RichAds Init] Повторная попытка инициализации после загрузки скрипта");
        initializeRichAdsAfterLoad();
      }, 1000);

      return;
    }

    // Если SDK доступен, продолжаем инициализацию
    initializeRichAdsAfterLoad();

  } catch (error) {
    console.error("[RichAds Init] КРИТИЧЕСКАЯ ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);

    // Блокируем все кнопки рекламы при ошибке
    if (watchAdButton) watchAdButton.disabled = true;
    if (watchVideoButton) watchVideoButton.disabled = true;
    if (openLinkButton) openLinkButton.disabled = true;
  }
}

/** Инициализирует RichAds SDK после загрузки скрипта. */
function initializeRichAdsAfterLoad() {
  try {
    console.log("[RichAds Init] Попытка создания экземпляра SDK...");

    // Проверяем, доступен ли SDK как функция
    if (typeof TelegramAdsController === "function") {
      console.log("[RichAds Init] SDK доступен как функция, создаем экземпляр");
      adsController = new TelegramAdsController();
      window.TelegramAdsController = adsController;
    }
    // Проверяем, доступен ли SDK как объект
    else if (typeof TelegramAdsController === "object" && TelegramAdsController !== null) {
      console.log("[RichAds Init] SDK доступен как объект, используем его");
      adsController = TelegramAdsController;
    }
    else {
      throw new Error("SDK RichAds (TelegramAdsController) не найден или имеет неверный тип");
    }

    // Проверяем наличие метода initialize
    if (!adsController || typeof adsController.initialize !== 'function') {
      throw new Error("Метод initialize не найден в SDK");
    }

    // Инициализируем SDK
    console.log("[RichAds Init] Вызов метода initialize с параметрами:", {
      pubId: MY_PUB_ID,
      appId: MY_APP_ID,
      debug: DEBUG_MODE
    });

    adsController.initialize({
      pubId: MY_PUB_ID,
      appId: MY_APP_ID,
      debug: DEBUG_MODE,
    });

    console.log("[RichAds Init] Инициализация SDK успешно вызвана");
    showStatus("Реклама готова.", "success");

    // Разблокируем все кнопки рекламы
    if (watchAdButton) watchAdButton.disabled = false;
    if (watchVideoButton) watchVideoButton.disabled = false;
    if (openLinkButton) openLinkButton.disabled = false;

    setTimeout(() => {
      if (statusMessageEl.textContent === "Реклама готова.") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[RichAds Init] ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);

    // Блокируем все кнопки рекламы при ошибке
    if (watchAdButton) watchAdButton.disabled = true;
    if (watchVideoButton) watchVideoButton.disabled = true;
    if (openLinkButton) openLinkButton.disabled = true;
  }
}

/** Обрабатывает нажатие на кнопку "Смотреть рекламу" - автопереход по баннеру-превью. */
function handleWatchAdClick() {
  // Проверка на блокировку кнопки или активный таймер
  if (isButtonPressed || (watchAdButton && watchAdButton.disabled)) {
    console.warn("[RichAds] Кнопка заблокирована или идет таймер - игнорируем клик");
    return;
  }

  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по рекламе во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос баннера-превью с автопереходом...");
  if (watchAdButton) watchAdButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Используем triggerNativeNotification с параметром true для автоперехода по баннеру-превью
    console.log("[RichAds] Запуск баннера-превью с автопереходом...");

    // Проверяем, доступен ли метод triggerNativeNotification
    if (typeof adsController.triggerNativeNotification === 'function') {
      console.log("[RichAds] Вызов triggerNativeNotification(true) для автоперехода по баннеру-превью");

      // Вызываем метод с параметром true для автоперехода
      adsController.triggerNativeNotification(true)
        .then((result) => {
          console.log("[RichAds] Успешный автопереход по баннеру-превью:", result);
          showStatus("Баннер-превью просмотрен! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            // Запускаем счетчик после успешного просмотра
            startCountdown(watchAdButton);
            tg.showPopup({
              title: window.appLocalization ? window.appLocalization.get('common.success') : "Успех!",
              message: window.appLocalization ? window.appLocalization.get('ads.ad_reward_credited') : "Награда за переход по баннеру-превью зачислена!",
              buttons: [{ type: "ok", text: window.appLocalization ? window.appLocalization.get('common.excellent') : "Отлично" }]
            });
          } else {
            // Если награда не зачислена, все равно запускаем счетчик
            startCountdown(watchAdButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка автоперехода по баннеру-превью:", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(watchAdButton);
          } else {
            // Пробуем альтернативный метод при ошибке
            if (typeof adsController.triggerInterstitialBanner === 'function') {
              console.log("[RichAds] Пробуем альтернативный метод triggerInterstitialBanner(true)");

              adsController.triggerInterstitialBanner(true)
                .then((result) => {
                  console.log("[RichAds] Успешный автопереход по баннеру (альтернативный метод):", result);
                  showStatus("Реклама просмотрена! Начисляем награду...", "info");
                  return recordAdView(AD_TYPES.NATIVE_BANNER);
                })
                .then((success) => {
                  if (success) {
                    startCountdown(watchAdButton);
                    tg.showPopup({
                      title: "Успех!",
                      message: "Награда за переход по баннеру зачислена!",
                      buttons: [{ type: "ok", text: "Отлично" }]
                    });
                  } else {
                    startCountdown(watchAdButton);
                  }
                })
                .catch((altError) => {
                  handleAdError(altError);
                  startCountdown(watchAdButton);
                })
                .finally(() => {
                  isAdShowing = false;
                });
            } else {
              handleAdError(error);
              startCountdown(watchAdButton);
              isAdShowing = false;
            }
          }
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[RichAds] Операция автоперехода по баннеру-превью завершена");
          isAdShowing = false;
        });
    }
    // Если triggerNativeNotification недоступен, пробуем triggerInterstitialBanner
    else if (typeof adsController.triggerInterstitialBanner === 'function') {
      console.log("[RichAds] Метод triggerNativeNotification недоступен, пробуем triggerInterstitialBanner(true)");

      adsController.triggerInterstitialBanner(true)
        .then((result) => {
          console.log("[RichAds] Успешный автопереход по баннеру:", result);
          showStatus("Реклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за переход по баннеру зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleAdError(error);
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[RichAds] Операция автоперехода по баннеру завершена");
          isAdShowing = false;
        });
    } else {
      // Если оба метода недоступны, выдаем ошибку
      throw new Error("Методы показа баннера-превью недоступны");
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове баннера-превью:", error);
    showStatus(`Ошибка показа баннера-превью: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа баннера-превью: ${error.message}`);
    if (watchAdButton) watchAdButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для обработки ошибок рекламы
  function handleAdError(error) {
    console.warn("[RichAds] Ошибка показа баннера-превью:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент баннер-превью недоступен";

    if (error instanceof Error) {
      reason = error.message;

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать баннер-превью. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();
      userFriendlyMessage = "Не удалось показать баннер-превью. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      } catch (e) {
        reason = "Баннер-превью недоступен или был закрыт";
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");
  }
}

/** Обрабатывает нажатие на кнопку "Смотреть видео" - показ видеорекламы. */
function handleWatchVideoClick() {
  // Проверка на блокировку кнопки или активный таймер
  if (isButtonPressed || (watchVideoButton && watchVideoButton.disabled)) {
    console.warn("[RichAds] Кнопка видео заблокирована или идет таймер - игнорируем клик");
    return;
  }

  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по видео во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос видеорекламы...");
  if (watchVideoButton) watchVideoButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Проверяем доступные методы для показа видеорекламы
    console.log("[RichAds] Запуск видеорекламы...");

    // Проверяем, доступен ли метод triggerInterstitialVideo
    if (typeof adsController.triggerInterstitialVideo === 'function') {
      console.log("[RichAds] Вызов triggerInterstitialVideo для показа видеорекламы");

      // Вызываем метод для показа видеорекламы
      adsController.triggerInterstitialVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы:", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video');
        })
        .then((success) => {
          if (success) {
            startCountdown(watchVideoButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(watchVideoButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа видеорекламы:", error);

          // Пробуем альтернативный метод для видеорекламы при ошибке
          if (typeof adsController.showRewardedVideo === 'function') {
            console.log("[RichAds] Пробуем альтернативный метод showRewardedVideo для показа видеорекламы");

            // Используем специальный метод для показа видеорекламы
            adsController.showRewardedVideo()
              .then((result) => {
                console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
                showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
                return recordAdView('rewarded_video');
              })
              .then((success) => {
                if (success) {
                  tg.showPopup({
                    title: "Успех!",
                    message: "Награда за просмотр видео зачислена!",
                    buttons: [{ type: "ok", text: "Отлично" }]
                  });
                }
              })
              .catch((altError) => {
                handleVideoError(altError);
                startCountdown(watchVideoButton);
              })
              .finally(() => {
                isAdShowing = false;
              });
          } else {
            handleVideoError(error);
            startCountdown(watchVideoButton);
            isAdShowing = false;
          }
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    }
    // Если triggerInterstitialVideo недоступен, пробуем showRewardedVideo
    else if (typeof adsController.showRewardedVideo === 'function') {
      console.log("[RichAds] Метод triggerInterstitialVideo недоступен, пробуем showRewardedVideo");

      // Используем специальный метод для показа видеорекламы
      adsController.showRewardedVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleVideoError(error);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    }
    // Если предыдущие методы недоступны, пробуем triggerRewardedVideo
    else if (typeof adsController.triggerRewardedVideo === 'function') {
      console.log("[RichAds] Пробуем метод triggerRewardedVideo для показа видеорекламы");

      // Используем специальный метод для показа видеорекламы
      adsController.triggerRewardedVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleVideoError(error);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    } else {
      // Если все методы недоступны, выдаем ошибку
      throw new Error("Методы показа видеорекламы недоступны");
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове видеорекламы:", error);
    showStatus(`Ошибка показа видеорекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа видеорекламы: ${error.message}`);
    if (watchVideoButton) watchVideoButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для обработки ошибок видеорекламы
  function handleVideoError(error) {
    console.warn("[RichAds] Ошибка показа видеорекламы:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент видеореклама недоступна";

    if (error instanceof Error) {
      reason = error.message;

      // Проверяем, есть ли реклама
      if (reason && reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        return;
      }

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать видеорекламу. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();

      // Проверяем, есть ли реклама
      if (reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        return;
      }

      userFriendlyMessage = "Не удалось показать видеорекламу. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      } catch (e) {
        reason = "Видеореклама недоступна или была закрыта";
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");
  }
}

/** Обрабатывает нажатие на кнопку "Открыть ссылку" - показ полноэкранного баннера. */
function handleOpenLinkClick() {
  // Проверка на блокировку кнопки или активный таймер
  if (isButtonPressed || (openLinkButton && openLinkButton.disabled)) {
    console.warn("[RichAds] Кнопка ссылки заблокирована или идет таймер - игнорируем клик");
    return;
  }

  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по ссылке во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос полноэкранного баннера...");
  if (openLinkButton) openLinkButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Проверяем доступные методы для показа полноэкранного баннера
    console.log("[RichAds] Запуск полноэкранного баннера...");

    // Сначала проверяем наличие метода triggerInterstitialBanner для показа полноразмерного баннера
    if (typeof adsController.triggerInterstitialBanner === 'function') {
      console.log("[RichAds] Вызов triggerInterstitialBanner(false) для показа полноэкранного баннера без автоперехода");

      // Вызываем метод с параметром false для показа баннера без автоперехода
      adsController.triggerInterstitialBanner(false)
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            startCountdown(openLinkButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(openLinkButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(openLinkButton);
          } else {
            // Пробуем другие методы при ошибке
            tryAlternativeMethods(error);
          }
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
      return; // Выходим из функции, так как уже вызвали нужный метод
    }

    // Выводим все доступные методы в консоль для отладки
    const availableMethods = Object.keys(adsController).filter(key => typeof adsController[key] === 'function');
    console.log("[RichAds] Доступные методы в adsController:", availableMethods);

    // Ищем все методы, которые могут быть связаны с полноэкранной рекламой
    const interstitialMethods = availableMethods.filter(method =>
      method.toLowerCase().includes('interstitial') ||
      method.toLowerCase().includes('fullscreen') ||
      method.toLowerCase().includes('full') ||
      method.toLowerCase().includes('screen')
    );
    console.log("[RichAds] Потенциальные методы для полноэкранной рекламы:", interstitialMethods);

    // Проверяем наличие метода triggerInterstitial
    if (typeof adsController.triggerInterstitial === 'function') {
      console.log("[RichAds] Вызов triggerInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.triggerInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода triggerFullscreenBanner
    else if (typeof adsController.triggerFullscreenBanner === 'function') {
      console.log("[RichAds] Вызов triggerFullscreenBanner() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.triggerFullscreenBanner()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода showInterstitial
    else if (typeof adsController.showInterstitial === 'function') {
      console.log("[RichAds] Вызов showInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.showInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода displayInterstitial
    else if (typeof adsController.displayInterstitial === 'function') {
      console.log("[RichAds] Вызов displayInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.displayInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Если специальные методы недоступны, пробуем стандартные методы
    else {
      // Если найдены другие методы, связанные с interstitial, пробуем их
      if (interstitialMethods.length > 0) {
        const methodName = interstitialMethods[0];
        console.log(`[RichAds] Пробуем найденный метод ${methodName} для показа полноэкранного баннера`);

        try {
          // Пробуем вызвать найденный метод
          adsController[methodName]()
            .then((result) => {
              console.log(`[RichAds] Успешный показ полноэкранного баннера через ${methodName}:`, result);
              showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
              return recordAdView('interstitial_banner_view');
            })
            .then((success) => {
              if (success) {
                tg.showPopup({
                  title: "Успех!",
                  message: "Награда за просмотр полноэкранного баннера зачислена!",
                  buttons: [{ type: "ok", text: "Отлично" }]
                });
              }
            })
            .catch((error) => {
              console.warn(`[RichAds] Ошибка показа полноэкранного баннера через ${methodName}:`, error);
              tryAlternativeMethods(error);
            })
            .finally(() => {
              if (openLinkButton) openLinkButton.disabled = false;
              console.log(`[RichAds] Операция показа полноэкранного баннера через ${methodName} завершена`);
              isAdShowing = false;
            });
        } catch (error) {
          console.warn(`[RichAds] Ошибка при вызове метода ${methodName}:`, error);
          tryAlternativeMethods(error);
        }
      } else {
        // Если не найдены специальные методы, используем стандартные
        tryAlternativeMethods();
      }
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове полноэкранного баннера:", error);
    showStatus(`Ошибка показа баннера: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа баннера: ${error.message}`);
    if (openLinkButton) openLinkButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для попытки использования альтернативных методов
  function tryAlternativeMethods(originalError) {
    // Проверяем, доступен ли метод showInterstitialAd
    if (typeof adsController.showInterstitialAd === 'function') {
      console.log("[RichAds] Пробуем альтернативный метод showInterstitialAd()");

      // Вызываем метод для показа полноэкранного баннера
      adsController.showInterstitialAd()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера (альтернативный метод):", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            startCountdown(openLinkButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(openLinkButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера (альтернативный метод):", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(openLinkButton);
          } else {
            tryNextMethod(error);
          }
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера (альтернативный метод) завершена");
          isAdShowing = false;
        });
    } else {
      tryNextMethod(originalError);
    }

    // Вложенная функция для перехода к следующему методу
    function tryNextMethod(currentError) {
      // Проверяем, доступен ли метод triggerInterstitialBanner
      if (typeof adsController.triggerInterstitialBanner === 'function') {
        console.log("[RichAds] Пробуем альтернативный метод triggerInterstitialBanner(false) для показа полноэкранного баннера");

        // Вызываем метод с параметром false для показа баннера без автоперехода
        adsController.triggerInterstitialBanner(false)
          .then((result) => {
            console.log("[RichAds] Успешный показ полноэкранного баннера (альтернативный метод):", result);
            showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
            return recordAdView('interstitial_banner_view');
          })
          .then((success) => {
            if (success) {
              tg.showPopup({
                title: "Успех!",
                message: "Награда за просмотр полноэкранного баннера зачислена!",
                buttons: [{ type: "ok", text: "Отлично" }]
              });
            }
          })
          .catch((error) => {
            console.warn("[RichAds] Ошибка показа полноэкранного баннера (альтернативный метод):", error);

            // Пробуем третий альтернативный метод при ошибке
            tryFinalMethod(error);
          })
          .finally(() => {
            if (openLinkButton) openLinkButton.disabled = false;
            console.log("[RichAds] Операция показа полноэкранного баннера (альтернативный метод) завершена");
            isAdShowing = false;
          });
      } else {
        tryFinalMethod(currentError);
      }
    }

    // Вложенная функция для последней попытки
    function tryFinalMethod(currentError) {
      // Если triggerInterstitialBanner недоступен, пробуем triggerNativeNotification
      if (typeof adsController.triggerNativeNotification === 'function') {
        console.log("[RichAds] Пробуем последний альтернативный метод triggerNativeNotification(false)");

        adsController.triggerNativeNotification(false)
          .then((result) => {
            console.log("[RichAds] Успешный показ полноэкранного баннера (последний альтернативный метод):", result);
            showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
            return recordAdView('interstitial_banner_view');
          })
          .then((success) => {
            if (success) {
              tg.showPopup({
                title: "Успех!",
                message: "Награда за просмотр полноэкранного баннера зачислена!",
                buttons: [{ type: "ok", text: "Отлично" }]
              });
            }
          })
          .catch((error) => {
            handleBannerError(originalError || currentError || error);
          })
          .finally(() => {
            if (openLinkButton) openLinkButton.disabled = false;
            console.log("[RichAds] Операция показа полноэкранного баннера (последний альтернативный метод) завершена");
            isAdShowing = false;
          });
      } else {
        // Если все методы недоступны, выдаем ошибку
        handleBannerError(originalError || currentError || new Error("Методы показа полноэкранного баннера недоступны"));
        isAdShowing = false;
      }
    }
  }

  // Функция для обработки ошибок полноэкранного баннера
  function handleBannerError(error) {
    console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";

    if (error instanceof Error) {
      reason = error.message;

      // Проверяем, есть ли реклама
      if (reason && reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        startCountdown(openLinkButton);
        return;
      }

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать рекламу. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();

      // Проверяем, есть ли реклама
      if (reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        startCountdown(openLinkButton);
        return;
      }

      userFriendlyMessage = "Не удалось показать рекламу. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      } catch (e) {
        reason = "Полноэкранный баннер недоступен или был закрыт";
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");

    // Запускаем счетчик даже при ошибке
    startCountdown(openLinkButton);
  }
}

// --- Функции для Секции "Заработок" ---

/** Обновляет данные в секции вывода */
function updateWithdrawalSection() {
  if (earnBalanceAmountEl) earnBalanceAmountEl.textContent = currentUserBalance;
  if (availableWithdrawalEl)
    availableWithdrawalEl.textContent = currentUserBalance;
  if (withdrawalErrorEl) withdrawalErrorEl.style.display = "none";
  if (withdrawalAmountInput) withdrawalAmountInput.value = "";
  if (withdrawalAddressInput) withdrawalAddressInput.value = "";
  if (requestWithdrawalButton) requestWithdrawalButton.disabled = true;

  // Обновляем placeholder для адреса кошелька
  updateAddressPlaceholder();

  // Загружаем историю выплат
  loadAndDisplayWithdrawalHistory();
}

/** Обновляет placeholder для поля адреса кошелька в зависимости от выбранной криптовалюты */
function updateAddressPlaceholder() {
  if (!cryptoCurrencySelect || !withdrawalAddressInput) return;

  const selectedCurrency = cryptoCurrencySelect.value;

  // Устанавливаем соответствующий placeholder в зависимости от выбранной валюты
  switch (selectedCurrency) {
    case 'usdttrc20':
      withdrawalAddressInput.placeholder = window.appLocalization ?
        window.appLocalization.get('placeholders.enter_wallet_address') :
        "Введите адрес";
      break;
    case 'btc':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin-кошелька (например: 1XYZ...)";
      break;
    case 'eth':
      withdrawalAddressInput.placeholder = "Введите адрес Ethereum-кошелька (например: 0xXYZ...)";
      break;
    case 'ton':
      withdrawalAddressInput.placeholder = "Введите адрес TON-кошелька (например: UQA...)";
      break;
    case 'ltc':
      withdrawalAddressInput.placeholder = "Введите адрес Litecoin-кошелька (например: LXYZ...)";
      break;
    case 'bch':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin Cash-кошелька (например: qXYZ...)";
      break;
    case 'xrp':
      withdrawalAddressInput.placeholder = "Введите адрес Ripple-кошелька (например: rXYZ...)";
      break;
    case 'ada':
      withdrawalAddressInput.placeholder = "Введите адрес Cardano-кошелька (например: addr1...)";
      break;
    case 'dot':
      withdrawalAddressInput.placeholder = "Введите адрес Polkadot-кошелька (например: 1XYZ...)";
      break;
    default:
      withdrawalAddressInput.placeholder = "Введите адрес криптокошелька";
  }

  // Обновляем информацию о минимальной сумме и комиссиях
  const minAmountInfo = document.getElementById("min-amount-info");
  if (minAmountInfo && currencyData[selectedCurrency]) {
    const selectedCurrencyInfo = currencyData[selectedCurrency];
    const currencyName = selectedCurrencyInfo.name || selectedCurrency.toUpperCase();
    const minAmountCoins = selectedCurrencyInfo.minCoins.toLocaleString();
    const minAmountUsd = (selectedCurrencyInfo.minCoins * coinValue).toFixed(2);
    let feeText = '';

    if (appSettings.show_fees_to_user && selectedCurrencyInfo.networkFee !== undefined) {
      feeText = ` Комиссия сети: $${selectedCurrencyInfo.networkFee.toFixed(2)}`;
    } else if (selectedCurrencyInfo.networkFee !== undefined) {
      // Если комиссия есть, но скрыта, можно указать это или ничего не показывать
      // feeText = " (комиссия скрыта)"; 
    }

    minAmountInfo.textContent = `Минимальная сумма для ${currencyName}: ${minAmountCoins} монет ($${minAmountUsd}).${feeText}`;

    // Обновляем цвет в зависимости от статуса валюты из currencyData
    const status = selectedCurrencyInfo.status;
    let statusColor = '#666'; // Серый по умолчанию

    // Определяем цвет на основе статуса из currencyData (например, 'best', 'good', 'expensive')
    // Эти статусы должны быть консистентны с теми, что используются в калькуляторе
    if (status) {
      switch (status.toLowerCase()) {
        case 'best':
          statusColor = 'var(--cyber-success-color, #4CAF50)'; // Зеленый
          break;
        case 'normal':
        case 'good':
          statusColor = 'var(--cyber-info-color, #2196F3)'; // Синий
          break;
        case 'warning':
        case 'expensive':
          statusColor = 'var(--cyber-warning-color, #FF9800)'; // Оранжевый
          break;
        case 'bad':
          statusColor = 'var(--cyber-error-color, #F44336)'; // Красный
          break;
        default:
          statusColor = 'var(--cyber-text-secondary, #666)';
      }
    }
    minAmountInfo.style.color = statusColor;
  } else if (minAmountInfo) {
    minAmountInfo.textContent = "Выберите валюту для отображения информации.";
    minAmountInfo.style.color = 'var(--cyber-text-secondary, #666)';
  }

  // Проверяем валидность формы после изменения placeholder
  validateWithdrawalForm();
}

/** Обновляет placeholder для поля адреса кошелька БЕЗ вызова валидации (для избежания рекурсии) */
function updateAddressPlaceholderSafe() {
  if (!cryptoCurrencySelect || !withdrawalAddressInput) return;

  const selectedCurrency = cryptoCurrencySelect.value;

  // Устанавливаем соответствующий placeholder в зависимости от выбранной валюты
  switch (selectedCurrency) {
    case 'usdttrc20':
      withdrawalAddressInput.placeholder = window.appLocalization ?
        window.appLocalization.get('placeholders.enter_wallet_address') :
        "Введите адрес";
      break;
    case 'btc':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin-кошелька (например: 1XYZ...)";
      break;
    case 'eth':
      withdrawalAddressInput.placeholder = "Введите адрес Ethereum-кошелька (например: 0xXYZ...)";
      break;
    case 'ton':
      withdrawalAddressInput.placeholder = "Введите адрес TON-кошелька (например: UQA...)";
      break;
    case 'ltc':
      withdrawalAddressInput.placeholder = "Введите адрес Litecoin-кошелька (например: LXYZ...)";
      break;
    case 'bch':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin Cash-кошелька (например: qXYZ...)";
      break;
    case 'xrp':
      withdrawalAddressInput.placeholder = "Введите адрес Ripple-кошелька (например: rXYZ...)";
      break;
    case 'ada':
      withdrawalAddressInput.placeholder = "Введите адрес Cardano-кошелька (например: addr1...)";
      break;
    case 'dot':
      withdrawalAddressInput.placeholder = "Введите адрес Polkadot-кошелька (например: 1XYZ...)";
      break;
    default:
      withdrawalAddressInput.placeholder = "Введите адрес криптокошелька";
  }
  // НЕ вызываем validateWithdrawalForm() чтобы избежать рекурсии
}

/** Инициализирует подсказку для рекомендаций к выводу (заглушка) */
function initWithdrawalRecommendationsTooltip() {
  // Заглушка для избежания ошибки ReferenceError
  console.log('initWithdrawalRecommendationsTooltip: функция не реализована');
}

/** Проверяет обновления статусов выплат (заглушка) */
function checkWithdrawalStatusUpdates() {
  // Заглушка для избежания ошибки ReferenceError
  console.log('checkWithdrawalStatusUpdates: функция не реализована');
}

/** Проверяет валидность адреса для конкретной криптовалюты */
function isValidAddressForCurrency(address, currency) {
  if (!address || address.length === 0) return false;

  const trimmedAddress = address.trim();

  // Сначала проверяем специфичные форматы для каждой валюты
  switch (currency.toLowerCase()) {
    case 'ton':
      // TON адреса: UQA..., EQA..., kQA... (44-48 символов в base64)
      return /^[UEkuek]Q[A-Za-z0-9_-]{42,46}$/.test(trimmedAddress) ||
             /^[0-9a-fA-F]{64}$/.test(trimmedAddress); // Hex формат TON

    case 'eth':
      // Ethereum адреса: 0x + 40 hex символов
      return /^0x[a-fA-F0-9]{40}$/.test(trimmedAddress);

    case 'usdttrc20':
      // USDT TRC20 использует TRON адреса: T + 33 символа
      return /^T[A-Za-z1-9]{33}$/.test(trimmedAddress);

    case 'btc':
      // Bitcoin адреса: 1..., 3..., bc1...
      return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(trimmedAddress) || // Legacy/P2SH
             /^bc1[a-z0-9]{39,59}$/.test(trimmedAddress); // Bech32

    case 'ltc':
      // Litecoin адреса: L..., M..., ltc1...
      return /^[LM][a-km-zA-HJ-NP-Z1-9]{26,33}$/.test(trimmedAddress) ||
             /^ltc1[a-z0-9]{39,59}$/.test(trimmedAddress);

    case 'bch':
      // Bitcoin Cash адреса: q..., p... (CashAddr) или legacy формат
      return /^[qp][a-z0-9]{41}$/.test(trimmedAddress) ||
             /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(trimmedAddress);

    case 'xrp':
      // Ripple адреса: r + 25-34 символа
      return /^r[1-9A-HJ-NP-Za-km-z]{25,34}$/.test(trimmedAddress);

    case 'ada':
      // Cardano адреса: addr1...
      return /^addr1[a-z0-9]{98}$/.test(trimmedAddress) ||
             /^[A-Za-z0-9]{59,104}$/.test(trimmedAddress);

    case 'dot':
      // Polkadot адреса: 1... (SS58 формат)
      return /^1[a-km-zA-HJ-NP-Z1-9]{46,47}$/.test(trimmedAddress);

    default:
      // Для неизвестных валют - минимальная проверка длины
      return trimmedAddress.length >= 20;
  }

  // УДАЛЕНО: Проверка Pioneer wallet перенесена в отдельную функцию
  // Теперь каждая валюта проверяется по своим правилам
}

/** Возвращает понятное сообщение об ошибке адреса для конкретной валюты */
function getInvalidAddressMessage(currency) {
  // ИСПРАВЛЕНИЕ: Сначала пробуем локализацию, но проверяем результат
  if (window.appLocalization && window.appLocalization.isLoaded) {
    let translationKey = '';
    switch (currency.toLowerCase()) {
      case 'ton':
        translationKey = 'validation.invalid_ton_address';
        break;
      case 'eth':
        translationKey = 'validation.invalid_eth_address';
        break;
      case 'usdttrc20':
        translationKey = 'validation.invalid_tron_address';
        break;
      case 'btc':
        translationKey = 'validation.invalid_btc_address';
        break;
      case 'ltc':
        translationKey = 'validation.invalid_ltc_address';
        break;
      case 'bch':
        translationKey = 'validation.invalid_bch_address';
        break;
      case 'xrp':
        translationKey = 'validation.invalid_xrp_address';
        break;
      case 'ada':
        translationKey = 'validation.invalid_ada_address';
        break;
      case 'dot':
        translationKey = 'validation.invalid_dot_address';
        break;
      default:
        translationKey = 'validation.invalid_address';
    }

    const translation = window.appLocalization.get(translationKey);
    // Если перевод найден (не равен ключу), используем его
    if (translation && translation !== translationKey) {
      return translation;
    }
  }

  // Fallback на русский язык (всегда работает)
  switch (currency.toLowerCase()) {
    case 'ton':
      return "Некорректный адрес. Используйте TON-адрес (UQ, EQ или kQ)";

    case 'eth':
      return "Неверный формат Ethereum-адреса. Должен начинаться с 0x";

    case 'usdttrc20':
      return "Неверный формат TRON-адреса. Должен начинаться с T";

    case 'btc':
      return "Неверный формат Bitcoin-адреса. Должен начинаться с 1, 3 или bc1";

    case 'ltc':
      return "Неверный формат Litecoin-адреса. Должен начинаться с L, M или ltc1";

    case 'bch':
      return "Неверный формат Bitcoin Cash-адреса. Должен начинаться с q, p, 1 или 3";

    case 'xrp':
      return "Неверный формат Ripple-адреса. Должен начинаться с r";

    case 'ada':
      return "Неверный формат Cardano-адреса. Должен начинаться с addr1";

    case 'dot':
      return "Неверный формат Polkadot-адреса. Должен начинаться с 1";

    default:
      return "Неверный формат адреса кошелька";
  }
}

/** Проверяет, является ли адрес адресом Pioneer wallet */
function isPioneerWalletAddress(address) {
  // Pioneer wallet обычно использует Ethereum-формат (0x...)
  // но может поддерживать и другие форматы
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

/** Проверяет совместимость Pioneer wallet с конкретной валютой */
function isPioneerWalletCompatibleWithCurrency(address, currency) {
  // Pioneer wallet поддерживает множество валют через один адрес
  // Но NOWPayments может не поддерживать автоконвертацию для всех

  const supportedCurrencies = [
    'eth',      // Ethereum - нативная поддержка
    'btc',      // Bitcoin - через автоконвертацию
    'usdttrc20', // USDT - НЕ поддерживается (нужен TRON адрес)
    'ltc',      // Litecoin - через автоконвертацию
    'bch',      // Bitcoin Cash - через автоконвертацию
    'xrp',      // Ripple - через автоконвертацию
    'ada',      // Cardano - через автоконвертацию
    'dot'       // Polkadot - через автоконвертацию
  ];

  // TON НЕ поддерживается Pioneer wallet (нужен специальный TON адрес)
  if (currency.toLowerCase() === 'ton') {
    return false;
  }

  // USDT TRC20 НЕ поддерживается (нужен TRON адрес)
  if (currency.toLowerCase() === 'usdttrc20') {
    return false;
  }

  return supportedCurrencies.includes(currency.toLowerCase());
}

/** Валидирует форму вывода средств и активирует/деактивирует кнопку */
function validateWithdrawalForm() {
  if (!withdrawalAmountInput || !withdrawalAddressInput || !requestWithdrawalButton || !cryptoCurrencySelect) return;

  const amountStr = withdrawalAmountInput.value.trim();
  const amount = parseInt(amountStr);
  const address = withdrawalAddressInput.value.trim();
  const currency = cryptoCurrencySelect.value;
  const currencyInfo = currencyData[currency];

  // НОВАЯ ЛОГИКА: Валидация на основе состояний и флагов
  const validationState = {
    hasMinBalance: currentUserBalance >= minBalanceForWithdrawal,
    hasValidAmount: amountStr && amountStr.trim() !== "" && !isNaN(amount) && amount > 0,
    hasEnoughBalance: !isNaN(amount) && amount <= currentUserBalance,
    meetsMinimum: !minWithdrawalAmount || (!isNaN(amount) && amount >= minWithdrawalAmount),
    hasAddress: address.length > 0,
    hasValidAddress: isValidAddressForCurrency(address, currency)
  };

  // Определяем приоритет ошибок
  let errorType = null;
  let errorMessage = "";

  if (!validationState.hasMinBalance) {
    errorType = 'MIN_BALANCE';
    errorMessage = `Недостаточно монет для вывода. Минимум: ${minBalanceForWithdrawal} монет`;
  } else if (!validationState.hasValidAmount) {
    errorType = 'INVALID_AMOUNT';
    errorMessage = "Введите корректную сумму";
  } else if (!validationState.hasEnoughBalance) {
    errorType = 'INSUFFICIENT_BALANCE';
    errorMessage = "Недостаточно монет на балансе";
  } else if (!validationState.meetsMinimum) {
    errorType = 'BELOW_MINIMUM';
    errorMessage = `Минимальная сумма для вывода: ${minWithdrawalAmount} монет`;
  } else if (!validationState.hasAddress) {
    errorType = 'NO_ADDRESS';
    errorMessage = "Введите адрес кошелька";
  } else if (!validationState.hasValidAddress) {
    errorType = 'INVALID_ADDRESS';
    errorMessage = getInvalidAddressMessage(currency);
  }

  const isValid = errorType === null;

  // Обновляем состояние кнопки
  requestWithdrawalButton.disabled = !isValid;

  // Управляем состоянием поля адреса на основе типа ошибки
  if (withdrawalAddressInput) {
    const shouldDisableAddress = errorType && ['MIN_BALANCE', 'INVALID_AMOUNT', 'INSUFFICIENT_BALANCE', 'BELOW_MINIMUM'].includes(errorType);

    if (shouldDisableAddress) {
      withdrawalAddressInput.disabled = true;
      withdrawalAddressInput.style.opacity = "0.5";
      withdrawalAddressInput.style.cursor = "not-allowed";
      withdrawalAddressInput.placeholder = "Сначала исправьте ошибки выше";
    } else {
      withdrawalAddressInput.disabled = false;
      withdrawalAddressInput.style.opacity = "1";
      withdrawalAddressInput.style.cursor = "text";
      // Восстанавливаем оригинальный placeholder (без вызова валидации)
      updateAddressPlaceholderSafe();
    }
  }

  // Показываем/скрываем сообщение об ошибке
  if (withdrawalErrorEl) {
    if (!isValid) { // Если форма НЕ валидна - показываем ошибку
      withdrawalErrorEl.innerHTML = errorMessage;
      withdrawalErrorEl.className = "hint error-message"; // Возвращаем класс ошибки
      withdrawalErrorEl.style.display = "block";
      // Убираем inline стили, чтобы работали CSS классы
      withdrawalErrorEl.style.backgroundColor = "";
      withdrawalErrorEl.style.borderColor = "";
      withdrawalErrorEl.style.color = "";
    } else {
      // Если форма валидна - СКРЫВАЕМ плашку полностью
      withdrawalErrorEl.style.display = "none";
      withdrawalErrorEl.innerHTML = "";
      withdrawalErrorEl.className = "";
    }
  }

  // Логируем состояние валидации для отладки
  console.log('🔍 Validation Debug:', {
    inputs: {
      amountStr: `"${amountStr}"`,
      amount,
      address: `"${address.substring(0, 20)}..."`,
      currency,
      currentUserBalance,
      minBalanceForWithdrawal,
      minWithdrawalAmount
    },
    validationState,
    result: {
      errorType,
      errorMessage,
      isValid
    }
  });
}



/** Получает данные пользователя */
function getUserDataForAPI() {
  // Получаем данные только из официального источника
  if (tg.initData) {
    return { initData: tg.initData };
  }

  // Если нет данных - возвращаем ошибку
  console.error('[API] Нет данных аутентификации Telegram');
  throw new Error('Ошибка аутентификации: отсутствуют данные Telegram');
}

  // БЕЗОПАСНОСТЬ: Никаких fake данных
  console.error('[API] ❌ Telegram WebApp недоступен - операция заблокирована');
  return null;
}

/** Загружает историю выплат пользователя */
async function loadWithdrawalHistory() {
  const userData = getUserDataForAPI();
  if (!userData) {
    console.warn("Нет данных пользователя для загрузки истории выплат");
    return;
  }

  const historyContainer = document.querySelector('.placeholder-list');
  if (!historyContainer) {
    console.warn("Не найден контейнер для истории выплат");
    return;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/getWithdrawalHistory.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`Ошибка: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Очищаем контейнер
    historyContainer.innerHTML = "";

    if (data.withdrawals && data.withdrawals.length > 0) {
      // Создаем список выплат
      data.withdrawals.forEach(withdrawal => {
        const withdrawalItem = document.createElement("div");
        withdrawalItem.className = "withdrawal-item";

        const date = new Date(withdrawal.timestamp * 1000);
        const dateStr = date.toLocaleDateString('ru-RU');
        const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

        // Определяем статус на русском
        const statusText = getWithdrawalStatusText(withdrawal.status);
        let statusClass = "";

        // Определяем CSS класс на основе статуса
        switch (withdrawal.status) {
          case 'waiting':
          case 'pending':
            statusClass = "status-pending";
            break;
          case 'processing':
          case 'sending':
            statusClass = "status-processing";
            break;
          case 'completed':
          case 'finished':
          case 'confirmed':
            statusClass = "status-completed";
            break;
          case 'failed':
          case 'cancelled':
          case 'rejected':
            statusClass = "status-failed";
            break;
          case 'expired':
            statusClass = "status-expired";
            break;
          default:
            statusClass = "status-unknown";
        }

        // Добавляем кнопку отмены для платежей в обработке
        const canCancel = ['waiting', 'pending', 'processing', 'sending'].includes(withdrawal.status);
        const cancelButton = canCancel ? `
          <div class="withdrawal-actions">
            <button class="cancel-button" onclick="cancelWithdrawal('${withdrawal.id}')">
              Отменить
            </button>
          </div>
        ` : '';

        // Формируем дополнительную информацию для завершенных выплат
        let additionalInfo = '';
        if (['finished', 'completed', 'confirmed'].includes(withdrawal.status)) {
          additionalInfo = '<div class="withdrawal-success-info">✅ Средства отправлены на ваш кошелек</div>';

          // Добавляем ссылку на блокчейн если есть hash транзакции
          if (withdrawal.transaction_hash || withdrawal.txid) {
            const hash = withdrawal.transaction_hash || withdrawal.txid;
            let blockchainUrl = '';

            switch (withdrawal.currency.toLowerCase()) {
              case 'eth':
                blockchainUrl = `https://etherscan.io/tx/${hash}`;
                break;
              case 'btc':
                blockchainUrl = `https://blockstream.info/tx/${hash}`;
                break;
              case 'usdttrc20':
              case 'trx':
                blockchainUrl = `https://tronscan.org/#/transaction/${hash}`;
                break;
              default:
                blockchainUrl = `https://etherscan.io/tx/${hash}`;
            }

            additionalInfo += `<div class="withdrawal-blockchain-link">
              <a href="${blockchainUrl}" target="_blank" rel="noopener">
                🔗 Посмотреть в блокчейне
              </a>
            </div>`;
          }
        } else if (['failed', 'rejected'].includes(withdrawal.status)) {
          additionalInfo = '<div class="withdrawal-error-info">❌ Выплата не удалась, средства возвращены</div>';
        } else if (['waiting', 'processing', 'sending'].includes(withdrawal.status)) {
          additionalInfo = '<div class="withdrawal-pending-info">⏳ Выплата обрабатывается, ожидайте</div>';
        }

        withdrawalItem.innerHTML = `
          <div class="withdrawal-header">
            <span class="withdrawal-amount">${withdrawal.coins_amount} ${window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет'}</span>
            <span class="withdrawal-status ${statusClass}">${statusText}</span>
          </div>
          <div class="withdrawal-details">
            <div class="withdrawal-currency">${withdrawal.currency.toUpperCase()}</div>
            <div class="withdrawal-date">${dateStr} ${timeStr}</div>
          </div>
          <div class="withdrawal-address">${withdrawal.wallet_address || withdrawal.address || 'Адрес не указан'}</div>
          ${additionalInfo}
          ${cancelButton}
        `;

        historyContainer.appendChild(withdrawalItem);
      });
    } else {
      historyContainer.innerHTML = "<p class='hint'>У вас пока нет заявок на вывод средств.</p>";
    }

  } catch (error) {
    console.error("Ошибка загрузки истории выплат:", error);
    historyContainer.innerHTML = "<p class='hint'>Не удалось загрузить историю выплат.</p>";
  }
}

/** Отменяет выплату по ID */
async function cancelWithdrawal(withdrawalId) {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  // Подтверждение отмены
  const confirmed = await new Promise((resolve) => {
    tg.showPopup({
      title: "Отмена выплаты",
      message: "Вы уверены, что хотите отменить эту выплату? Средства будут возвращены на ваш баланс.",
      buttons: [
        { type: "cancel", text: "Нет" },
        { type: "destructive", text: "Да, отменить" }
      ]
    }, (buttonId) => {
      resolve(buttonId === "destructive");
    });
  });

  if (!confirmed) return;

  try {
    showStatus("Отмена выплаты...");

    const response = await fetch(`${API_BASE_URL}/cancelWithdrawal.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        withdrawal_id: withdrawalId
      }),
    });

    if (!response.ok) {
      let errorText = `Ошибка: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.error) {
          errorText = errorData.error;
        }
      } catch (e) {}
      throw new Error(errorText);
    }

    const data = await response.json();

    // Обновляем баланс
    if (data.new_balance !== undefined) {
      updateBalanceDisplay(data.new_balance);
    }

    // Показываем успех
    showStatus("Выплата отменена, средства возвращены на баланс!", "success");
    tg.showPopup({
      title: "Успех!",
      message: `Выплата отменена. ${data.returned_amount || ''} монет возвращено на ваш баланс.`,
      buttons: [{ type: "ok", text: "Понятно" }]
    });

    // Обновляем историю выплат
    loadAndDisplayWithdrawalHistory();

  } catch (error) {
    console.error("Ошибка отмены выплаты:", error);
    showStatus(`Ошибка отмены: ${error.message}`, "error");
    tg.showAlert(`Не удалось отменить выплату:\n${error.message}`);
  }
}

/** Обработчик для кнопки "Запросить вывод" */
async function handleRequestWithdrawal() {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  const amount = parseInt(withdrawalAmountInput.value);
  const address = withdrawalAddressInput.value.trim();
  const currency = cryptoCurrencySelect.value;

  // Блокируем кнопку
  requestWithdrawalButton.disabled = true;
  showStatus("Расчет точной суммы...");

  try {
    // Сначала получаем точную сумму криптовалюты из серверного расчета
    const calcResponse = await fetch(`${API_BASE_URL}/calculateWithdrawalAmount.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        coins_amount: amount,
        currency: currency
      })
    });

    if (!calcResponse.ok) {
      throw new Error(`Ошибка расчета: ${calcResponse.status}`);
    }

    const calcData = await calcResponse.json();

    if (!calcData.success) {
      throw new Error(calcData.error || 'Ошибка расчета суммы');
    }

    const exactCryptoAmount = calcData.crypto_amount;
    console.log(`Точная сумма для вывода: ${exactCryptoAmount} ${currency.toUpperCase()}`);

    showStatus("Отправка запроса на вывод...");

    // Теперь отправляем запрос с точной суммой криптовалюты
    const response = await fetch(`${API_BASE_URL}/requestWithdrawal.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        amount: amount, // Количество монет для списания с баланса
        crypto_amount: exactCryptoAmount, // Точная сумма криптовалюты для вывода
        crypto_address: address,
        crypto_currency: currency
      }),
    });

    if (!response.ok) {
      let errorText = `Ошибка: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.error) {
          errorText = errorData.error;
        }
      } catch (e) {}
      throw new Error(errorText);
    }

    const data = await response.json();

    // Обновляем баланс
    updateBalanceDisplay(data.new_balance);

    // Формируем сообщение с учетом типа конвертации
    let successMessage = `Запрос на вывод ${amount} монет в ${currency.toUpperCase()} создан!`;
    let statusMessage = "Запрос на вывод создан!";

    // Добавляем информацию о комиссиях, если есть
    if (data.fee_handling) {
      successMessage += `\n\n💳 Информация о комиссии:\n${data.fee_handling.note}`;

      if (data.fee_handling.fee_estimate && data.fee_handling.fee_estimate.fee) {
        const fee = data.fee_handling.fee_estimate.fee;
        const feeCurrency = data.fee_handling.fee_estimate.currency || currency.toUpperCase();
        successMessage += `\n📊 Комиссия: ${fee} ${feeCurrency}`;
      }

      if (data.fee_handling.min_amount) {
        successMessage += `\n📏 Минимум: ${data.fee_handling.min_amount} ${currency.toUpperCase()}`;
      }
    }

    // Проверяем, была ли использована внутренняя конвертация NOWPayments
    if (data.internal_conversion) {
      const internalConv = data.internal_conversion;
      const targetCurrency = internalConv.target_currency.toUpperCase();
      const targetAmount = internalConv.target_amount;

      successMessage += `\n\n⚡ Использована внутренняя конвертация NOWPayments`;
      successMessage += `\n💰 Выплата: ${targetAmount} ${targetCurrency}`;
      statusMessage = "Выплата создана через внутреннюю конвертацию!";

      console.log("Внутренняя конвертация NOWPayments:", internalConv);
    }
    // Проверяем, была ли использована внешняя автоконвертация
    else if (data.auto_conversion) {
      const autoConv = data.auto_conversion;
      const actualCurrency = autoConv.actual_payout.currency.toUpperCase();
      const actualAmount = autoConv.actual_payout.amount;

      successMessage += `\n\n🔄 Использована внешняя автоконвертация из ${actualCurrency}`;
      successMessage += `\n💰 Выплата: ${actualAmount} ${actualCurrency}`;
      statusMessage = "Выплата создана через автоконвертацию!";

      console.log("Внешняя автоконвертация:", autoConv);
    }

    // Показываем успех
    showStatus(statusMessage, "success");
    tg.showPopup({
      title: "Успех!",
      message: successMessage,
      buttons: [{ type: "ok", text: "Отлично" }]
    });

    // Очищаем форму
    withdrawalAmountInput.value = "";
    withdrawalAddressInput.value = "";

    // Обновляем секцию вывода (включая историю) с задержкой
    console.log('[Withdrawal] Выплата создана успешно, обновляем историю через 1000мс');
    setTimeout(() => {
      console.log('[Withdrawal] Вызываем updateWithdrawalSection()');
      updateWithdrawalSection();

      // Дополнительно принудительно загружаем историю
      setTimeout(() => {
        console.log('[Withdrawal] Принудительная загрузка истории');
        loadAndDisplayWithdrawalHistory();
      }, 500);
    }, 1000);

  } catch (error) {
    console.error("Ошибка вывода:", error);

    // Специальная обработка различных типов ошибок
    if (error.message.includes('не подходит для валюты') ||
        error.message.includes('Автоконвертация невозможна') ||
        error.message.includes('INCOMPATIBLE_ADDRESS')) {

      // Показываем детальное объяснение пользователю
      const currency = cryptoCurrencySelect.value.toUpperCase();
      const address = withdrawalAddressInput.value.trim();

      let helpMessage = `Ваш адрес не подходит для автоконвертации в ${currency}.\n\n`;
      helpMessage += `💡 Решения:\n`;
      helpMessage += `1. Укажите адрес, совместимый с ${currency}\n`;
      helpMessage += `2. Выберите валюту, совместимую с вашим адресом\n`;
      helpMessage += `3. Обратитесь в поддержку за помощью`;

      showStatus("Адрес несовместим с выбранной валютой", "error");

      tg.showPopup({
        title: "Несовместимый адрес",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else if (error.message.includes('нужен баланс TON') ||
               error.message.includes('NEED_TON_BALANCE')) {

      // Специальная обработка для TON адресов
      let helpMessage = `Для выплаты на TON кошелек требуется баланс TON.\n\n`;
      helpMessage += `💡 Решения:\n`;
      helpMessage += `1. Пополните баланс TON в панели NOWPayments\n`;
      helpMessage += `2. Используйте BTC адрес для автоконвертации\n`;
      helpMessage += `3. Обратитесь к администратору для пополнения баланса`;

      showStatus("Нужен баланс TON для TON адресов", "error");

      tg.showPopup({
        title: "Требуется пополнение баланса",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else if (error.message.includes('Минимальная сумма для выплаты')) {

      // Специальная обработка для ошибок минимальной суммы
      let helpMessage = error.message;

      // Пытаемся извлечь детали из ошибки
      const minAmountMatch = error.message.match(/Минимальная сумма для выплаты (\w+): ([\d.]+)/);
      if (minAmountMatch) {
        const currency = minAmountMatch[1];
        const minAmount = minAmountMatch[2];

        helpMessage += `\n\n💡 Решения:\n`;
        helpMessage += `1. Увеличьте сумму до ${minAmount} ${currency} или больше\n`;
        helpMessage += `2. Выберите другую криптовалюту с меньшим минимумом\n`;
        helpMessage += `3. Накопите больше монет для вывода`;
      }

      showStatus("Сумма меньше минимальной", "error");

      tg.showPopup({
        title: "Сумма слишком мала",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else if (error.message.includes('Превышен лимит выводов средств') ||
               error.message.includes('withdrawal limit exceeded') ||
               error.message.includes('Попробуйте позже')) {

      // Специальная обработка для ошибки лимита выводов
      showStatus("Превышен лимит выводов средств", "error");

      tg.showPopup({
        title: "Лимит выводов",
        message: "Превышен лимит выводов средств. Попробуйте позже.\n\n💡 Информация:\n• Максимум 3 вывода в день\n• Лимит сбрасывается каждые 24 часа",
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else {
      // Обычная обработка других ошибок
      showStatus(`Ошибка: ${error.message}`, "error");
    }

    // Показываем ошибку в форме с правильным классом
    if (withdrawalErrorEl) {
      withdrawalErrorEl.textContent = error.message;
      withdrawalErrorEl.className = "hint error-message"; // Принудительно устанавливаем класс ошибки
      withdrawalErrorEl.style.display = "block";
      // Убираем inline стили, чтобы работали CSS классы
      withdrawalErrorEl.style.backgroundColor = "";
      withdrawalErrorEl.style.borderColor = "";
      withdrawalErrorEl.style.color = "";
    }

    // Разблокируем кнопку
    requestWithdrawalButton.disabled = false;
  }
}

// --- Функции для Секции "Друзья" ---

/** Генерирует и отображает реферальную ссылку пользователя. */
function generateReferralLink() {
  if (currentUserId && referralLinkInput) {
    const link = `https://t.me/${BOT_USERNAME}?start=${currentUserId}`;
    referralLinkInput.value = link;
    if (copyReferralButton) copyReferralButton.disabled = false;
  } else if (referralLinkInput) {
    referralLinkInput.value = "Загрузка...";
    if (copyReferralButton) copyReferralButton.disabled = true;
  }
}

/** Копирует реферальную ссылку в буфер обмена (с Fallback) */
function copyReferralLink() {
  if (!referralLinkInput) {
    tg.showAlert("Ошибка: Не найден элемент ссылки.");
    return;
  }
  const textToCopy = referralLinkInput.value;
  if (navigator.clipboard && window.isSecureContext) {
    console.log("Копирование через navigator.clipboard...");
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        console.log("Успех clipboard.");
        showSuccessCopyFeedback();
      })
      .catch((err) => {
        console.warn("Ошибка navigator.clipboard:", err);
        tryFallbackCopy(textToCopy);
      });
  } else {
    console.log("navigator.clipboard недоступен, пробуем fallback.");
    tryFallbackCopy(textToCopy);
  }
}

/** Загружает и отображает статистику рефералов */
async function loadReferralStats() {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  const referralsCountEl = document.getElementById("referrals-count");
  const referralEarningsEl = document.getElementById("referral-earnings");
  const referralsListEl = document.getElementById("referrals-list");
  const refreshStatsButton = document.getElementById("refresh-stats-button");

  if (!referralsCountEl || !referralEarningsEl || !referralsListEl) {
    console.error("Не найдены элементы для отображения статистики рефералов");
    console.error("referralsCountEl:", referralsCountEl);
    console.error("referralEarningsEl:", referralEarningsEl);
    console.error("referralsListEl:", referralsListEl);
    return;
  }

  // Отладка: проверяем текущее содержимое элементов
  console.log("[Referral Stats] Текущее содержимое referralEarningsEl:", referralEarningsEl.textContent);

  // Отключаем кнопку обновления на время загрузки
  if (refreshStatsButton) refreshStatsButton.disabled = true;

  showStatus("Загрузка статистики рефералов...");

  try {
    const response = await fetch(`${API_BASE_URL}/getReferralStats.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();

    // Отладочная информация
    console.log("[Referral Stats] Получены данные:", data);
    console.log("[Referral Stats] totalEarned:", data.totalEarned);
    console.log("[Referral Stats] referralsCount:", data.referralsCount);

    // Обновляем счетчики
    referralsCountEl.textContent = data.referralsCount || 0;
    const coinsText = window.appLocalization ?
      window.appLocalization.get('currency.coins') :
      'монет';
    referralEarningsEl.textContent = (data.totalEarned || 0) + " " + coinsText;

    // Дополнительная отладка
    console.log("[Referral Stats] Установлен текст заработка:", referralEarningsEl.textContent);

    // Проверяем что значение действительно установилось
    setTimeout(() => {
      console.log("[Referral Stats] Проверка через 100мс:", referralEarningsEl.textContent);
    }, 100);

    // Обновляем информацию о реферере в разделе "Подписки"
    const subscriptionsListEl = document.getElementById("subscriptions-list");
    if (subscriptionsListEl) {
      if (data.referrerInfo) {
        subscriptionsListEl.innerHTML = `
          <div class="referral-item">
            <div class="referral-name">Вас пригласил: ${data.referrerInfo.display_name}</div>
          </div>
        `;
      } else {
        subscriptionsListEl.innerHTML = "<p class='hint'>Вы не были приглашены другим пользователем.</p>";
      }
    }

    // Обновляем список рефералов
    if (data.referrals && data.referrals.length > 0) {
      // Очищаем список
      referralsListEl.innerHTML = "";

      // Добавляем каждого реферала в список
      data.referrals.forEach(referral => {
        const referralItem = document.createElement("div");
        referralItem.className = "referral-item";

        const referralName = document.createElement("div");
        referralName.className = "referral-name";

        // Используем display_name, если он есть, иначе показываем ID
        if (referral.display_name) {
          referralName.textContent = referral.display_name;
        } else {
          referralName.textContent = `ID: ${referral.id}`;
        }

        const referralInfo = document.createElement("div");
        referralInfo.className = "referral-info";

        // Форматируем дату
        const joinDate = new Date(referral.joined * 1000);
        const dateStr = joinDate.toLocaleDateString();

        referralInfo.textContent = `Баланс: ${referral.balance} • Присоединился: ${dateStr}`;

        referralItem.appendChild(referralName);
        referralItem.appendChild(referralInfo);

        referralsListEl.appendChild(referralItem);
      });
    } else {
      referralsListEl.innerHTML = "<p class='hint'>У вас пока нет рефералов. Пригласите друзей!</p>";
    }

    showStatus("Статистика рефералов обновлена", "success");
    setTimeout(() => {
      if (statusMessageEl.textContent === "Статистика рефералов обновлена") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[Referral Stats] Ошибка:", error);
    showStatus(`Ошибка загрузки статистики: ${error.message}`, "error");
    referralsListEl.innerHTML = "<p class='hint'>Не удалось загрузить данные о рефералах.</p>";
  } finally {
    // Включаем кнопку обновления
    if (refreshStatsButton) refreshStatsButton.disabled = false;
  }
}

/** Регистрирует пользователя как реферала, если он перешел по реферальной ссылке */
async function registerAsReferral() {
  // Проверяем, есть ли параметр start в URL
  const urlParams = new URLSearchParams(window.location.search);
  const startParam = urlParams.get('start');

  if (!startParam || !tg.initData) {
    // Нет параметра start или данных Telegram, значит это не реферальный переход
    return;
  }

  console.log(`[Referral] Обнаружен параметр start: ${startParam}`);
  showStatus("Регистрация реферала...");

  try {
    const response = await fetch(`${API_BASE_URL}/registerReferral.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        referrerId: startParam
      }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();

    if (data.success) {
      showStatus("Вы успешно зарегистрированы как реферал!", "success");
      tg.showPopup({
        title: "Успех!",
        message: "Вы успешно зарегистрированы как реферал!",
        buttons: [{ type: "ok", text: "Отлично" }]
      });
    } else {
      console.log(`[Referral] Регистрация не требуется: ${data.message}`);
    }
  } catch (error) {
    console.error("[Referral Registration] Ошибка:", error);
    // Не показываем ошибку пользователю, чтобы не мешать основному использованию приложения
  } finally {
    showStatus("");
  }
}
/** Fallback функция для копирования через document.execCommand */
function tryFallbackCopy(textToCopy) {
  console.log("Копирование через document.execCommand...");
  try {
    const textArea = document.createElement("textarea");
    textArea.value = textToCopy;
    textArea.style.position = "absolute";
    textArea.style.left = "-9999px";
    document.body.appendChild(textArea);
    textArea.select();
    const successful = document.execCommand("copy");
    document.body.removeChild(textArea);
    if (successful) {
      console.log("Успех execCommand.");
      showSuccessCopyFeedback();
    } else {
      throw new Error("execCommand вернул false");
    }
  } catch (err) {
    console.error("Ошибка document.execCommand:", err);
    tg.showAlert(
      "Не удалось скопировать ссылку. Выделите и скопируйте вручную."
    );
    if (referralLinkInput) {
      try {
        referralLinkInput.focus();
        referralLinkInput.select();
        referralLinkInput.setSelectionRange(0, 99999);
      } catch (e) {}
    }
    tg.HapticFeedback.notificationOccurred("error");
  }
}
/** Показывает визуальную обратную связь при успешном копировании */
function showSuccessCopyFeedback() {
  tg.HapticFeedback.notificationOccurred("success");
  showStatus("Ссылка скопирована!", "success");
  if (copyReferralButton) {
    const t = copyReferralButton.textContent;
    copyReferralButton.textContent = "✅";
    setTimeout(() => {
      copyReferralButton.textContent = t;
      if (statusMessageEl.textContent === "Ссылка скопирована!") showStatus("");
    }, 1500);
  }
}
/** Обработчик нажатия на кнопку "Поделиться приложением" */
function handleShareAppClick() {
  console.log("Попытка поделиться...");
  tg.HapticFeedback.impactOccurred("light");

  // Получаем ID текущего пользователя для реферальной ссылки
  const userId = currentUserId || (tg.initDataUnsafe?.user?.id);

  // Формируем реферальную ссылку с параметром start
  const shareUrl = userId ?
    `https://t.me/${BOT_USERNAME}?start=${userId}` :
    `https://t.me/${BOT_USERNAME}`;

  const shareText = window.appLocalization ?
    window.appLocalization.get('friends.share_text') :
    `Просто смотри рекламу и получай крипту на карту. Мгновенный вывод на кошелёк:`;

  console.log(`Формируем реферальную ссылку для поделиться: ${shareUrl}`);

  try {
    console.log(`Попытка tg.shareApp`);
    tg.shareApp({ url: shareUrl, text: shareText });
    console.log("shareApp вызван.");
    return;
  } catch (shareError) {
    console.warn("Ошибка tg.shareApp(options):", shareError);
  }
  try {
    const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(
      shareUrl
    )}&text=${encodeURIComponent(shareText)}`;
    console.log(`Попытка tg.openTelegramLink: ${telegramShareUrl}`);
    tg.openTelegramLink(telegramShareUrl);
    console.log("openTelegramLink вызван.");
  } catch (linkError) {
    console.error("Ошибка tg.openTelegramLink:", linkError);
    tg.showAlert("Не удалось открыть окно 'Поделиться'.");
  }
}

// --- Инициализация Приложения ---

/** Основная функция инициализации Mini App. */
function initializeApp() {
  console.log(`Инициализация ${BOT_USERNAME} App (v с анимацией v6)...`);
  tg.ready();
  tg.expand();

  const requiredElements = [
    mainContentEl,
    earnSectionEl,
    friendsSectionEl,
    userNameEl,
    balanceAmountEl,
    headerBalanceInfoEl,
    watchAdButton,
    watchVideoButton,
    openLinkButton,
    statusMessageEl,
    shareAppButton,
    referralLinkInput,
    copyReferralButton,
    navHomeButton,
    navEarnButton,
    navFriendsButton,
    earnBalanceAmountEl,
    availableWithdrawalEl,
    minWithdrawalEl,
    withdrawalAmountInput,
    withdrawalAddressInput,
    cryptoCurrencySelect,
    requestWithdrawalButton,
    withdrawalErrorEl,
  ];
  
  // Обработчик ручного изменения суммы вывода
  if (withdrawalAmountInput) {
    withdrawalAmountInput.addEventListener('input', function() {
      const amount = parseInt(this.value) || 0;
      const calcAmountInput = document.getElementById('calc-amount');

      if (calcAmountInput) {
        calcAmountInput.value = amount;
        updateCalculatorDisplay(amount);
      }

      // Автоматически обновляем поле криптовалюты
      updateCryptoAmountField();
    });
  }
  const missingElement = requiredElements.find((el) => !el);
  if (missingElement) {
    console.error(
      "КРИТИЧЕСКАЯ ОШИБКА: Не найден элемент интерфейса!",
      missingElement
    );
    showStatus("Ошибка загрузки интерфейса!", "error");
    tg.showAlert("Ошибка интерфейса приложения.");
    return;
  }

  // Устанавливаем начальное состояние интерфейса
  allPages.forEach((page) => {
    if (page && page !== mainContentEl) {
      page.classList.add("page-hidden");
      page.style.display = ""; /* Сбрасываем инлайн стиль при инициализации */
    }
  });

  // 🔄 ВОССТАНОВЛЕНИЕ АКТИВНОЙ СТРАНИЦЫ после reload
  const lastActivePage = localStorage.getItem('lastActivePage');
  let initialPage = mainContentEl;
  let initialNavButton = navHomeButton;

  if (lastActivePage) {
    console.log(`[Page Restore] Восстанавливаем последнюю активную страницу: ${lastActivePage}`);

    // Определяем какую страницу восстановить
    switch (lastActivePage) {
      case 'earn-section':
        initialPage = earnSectionEl;
        initialNavButton = navEarnButton;
        break;
      case 'friends-section':
        initialPage = friendsSectionEl;
        initialNavButton = navFriendsButton;
        break;
      default:
        initialPage = mainContentEl;
        initialNavButton = navHomeButton;
    }

    // Очищаем сохраненную страницу
    localStorage.removeItem('lastActivePage');
  }

  // Устанавливаем активную страницу
  allPages.forEach((page) => {
    if (page === initialPage) {
      page.classList.remove("page-hidden");
      page.style.display = "";
    } else {
      page.classList.add("page-hidden");
    }
  });

  currentPageElement = initialPage;
  updateActiveNavButton(initialNavButton);

  console.log(`[Page Restore] Активная страница установлена: ${currentPageElement.id}`);

  // Блокируем только кнопки действий, НЕ навигационные кнопки
  if (watchAdButton) watchAdButton.disabled = true;
  if (watchVideoButton) watchVideoButton.disabled = true;
  if (openLinkButton) openLinkButton.disabled = true;
  if (copyReferralButton) copyReferralButton.disabled = true;
  if (requestWithdrawalButton) requestWithdrawalButton.disabled = true;

  // Назначение обработчиков событий
  if (navHomeButton) {
    navHomeButton.addEventListener("click", showMainContent);
    navHomeButton.disabled = false;
  }
  if (navEarnButton) {
    navEarnButton.addEventListener("click", showEarnSection);
    navEarnButton.disabled = false;
  }
  if (navFriendsButton) {
    navFriendsButton.addEventListener("click", showFriendsSection);
    navFriendsButton.disabled = false;
  }
  if (headerBalanceInfoEl) {
    headerBalanceInfoEl.addEventListener("click", showEarnSection);
  }
  watchAdButton.addEventListener("click", handleWatchAdClick);
  watchVideoButton.addEventListener("click", handleWatchVideoClick);
  openLinkButton.addEventListener("click", handleOpenLinkClick);
  shareAppButton.addEventListener("click", handleShareAppClick);
  copyReferralButton.addEventListener("click", copyReferralLink);
  requestWithdrawalButton.addEventListener("click", handleRequestWithdrawal);

  // Инициализация формы вывода средств
  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.addEventListener("change", updateAddressPlaceholder);
  }

  // Добавляем обработчики для валидации формы в реальном времени
  if (withdrawalAmountInput) {
    withdrawalAmountInput.addEventListener("input", () => {
      validateWithdrawalForm();
      updateCryptoAmountField();
    });
  }

  if (withdrawalAddressInput) {
    withdrawalAddressInput.addEventListener("input", validateWithdrawalForm);
  }

  // Добавляем обработчик для изменения криптовалюты
  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.addEventListener("change", updateCryptoAmountField);
  }

  // Добавляем обработчик для кнопки обновления статистики рефералов
  const refreshStatsButton = document.getElementById("refresh-stats-button");
  if (refreshStatsButton) {
    refreshStatsButton.addEventListener("click", loadReferralStats);
  }

  // Запуск асинхронных операций
  showStatus("Загрузка данных...", "info");

  console.log("Инициализация приложения начата...");

  // Загружаем настройки приложения
  loadAppSettings().then(() => {
    console.log("Настройки приложения загружены");
  });

  // Проверяем, есть ли параметр start в URL для регистрации реферала
  registerAsReferral();

  // Инициализируем локализацию (неблокирующая)
  if (window.appLocalization) {
    console.log("Локализация инициализирована");

    // Принудительно применяем переводы, если они уже загружены
    if (window.appLocalization.isLoaded) {
      window.appLocalization.applyTranslations();
      console.log("Переводы применены при инициализации");
    }
  }

  fetchUserData()
    .then(() => {
      console.log("Загрузка данных пользователя завершена.");

      // Загружаем статистику рефералов, если мы на странице друзей
      if (currentPageElement === friendsSectionEl) {
        loadReferralStats();
      }

      if (typeof TelegramAdsController === "function") {
        initializeRichAds();
      } else {
        console.warn("Ожидание RichAds SDK...");
        showStatus("Ожидание модуля рекламы...", "info");
        let attempts = 0;
        const maxAttempts = 50;
        const interval = 100;
        const checkInterval = setInterval(() => {
          attempts++;
          if (typeof TelegramAdsController === "function") {
            clearInterval(checkInterval);
            initializeRichAds();
          } else if (attempts >= maxAttempts) {
            clearInterval(checkInterval);
            console.error("Тайм-аут RichAds SDK");
            showStatus("Ошибка: Модуль рекламы не загружен.", "error");
            tg.showAlert("Не удалось загрузить модуль рекламы.");

            // Блокируем все кнопки рекламы при тайм-ауте
            if (watchAdButton) watchAdButton.disabled = true;
            if (watchVideoButton) watchVideoButton.disabled = true;
            if (openLinkButton) openLinkButton.disabled = true;
          }
        }, interval);
      }
    })
    .catch((err) => {
      console.error("Ошибка fetchUserData:", err);
      showStatus("Ошибка загрузки данных.", "error");
      tg.showAlert("Ошибка загрузки данных.");

      // Блокируем все кнопки рекламы при ошибке загрузки данных
      if (watchAdButton) watchAdButton.disabled = true;
      if (watchVideoButton) watchVideoButton.disabled = true;
      if (openLinkButton) openLinkButton.disabled = true;
    });
  console.log("Инициализация настроена.");
}

// --- Точка Входа ---
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
    initializeWithdrawalCalculator();
    // Инициализируем иконку и информацию о валюте по умолчанию (TON)
    updateCurrencyCardIcon('ton');
    updateCurrencyInfo('ton');
  });
} else {
  initializeApp();
  initializeWithdrawalCalculator();
  // Инициализируем иконку и информацию о валюте по умолчанию (TON)
  updateCurrencyCardIcon('ton');
  updateCurrencyInfo('ton');
}

// === КАЛЬКУЛЯТОР ВЫВОДА ===

/**
 * Данные о валютах с актуальными комиссиями
 */
const currencyData = {
  eth: {
    name: 'Ethereum (ETH)',
    minCoins: 564, // ОБНОВЛЕНО: 564 монет с учетом комиссии NOWPayments
    networkFee: 0.25, // Комиссия $0.25 (уменьшена для нового курса)
    status: 'best'
  },
  btc: {
    name: 'Bitcoin (BTC)',
    minCoins: 1500, // ИСПРАВЛЕНО: 1500 монет с учетом комиссии NOWPayments
    networkFee: 0.50, // Комиссия $0.50 (уменьшена для нового курса)
    status: 'good'
  },
  usdttrc20: {
    name: 'USDT (TRC20)',
    minCoins: 12924, // ОБНОВЛЕНО: 12924 монет с учетом комиссии NOWPayments
    networkFee: 5.58, // Средняя комиссия $5.58 (оставляем как есть)
    status: 'expensive'
  },
  ton: {
    name: 'TON (Telegram)',
    minCoins: 1489, // ОБНОВЛЕНО: 1489 монет с учетом комиссии NOWPayments
    networkFee: 0.15, // Комиссия $0.15 (низкая комиссия TON)
    status: 'best'
  }
};

// ИСПРАВЛЕНИЕ: Экспортируем currencyData в глобальную область для других модулей
window.currencyData = currencyData;

/**
 * Инициализирует калькулятор вывода
 */
function initializeWithdrawalCalculator() {
  const calcAmountInput = document.getElementById('calc-amount');
  const calcBalance = document.getElementById('calc-balance');
  const currencyTabs = document.querySelectorAll('.currency-tab');

  if (!calcAmountInput) return;

  // Инициализируем табы валют
  initializeCurrencyTabs();

  // Обновляем баланс в калькуляторе
  if (calcBalance) {
    calcBalance.textContent = `${currentUserBalance || 0} монет`;
  }

  // Обработчик изменения суммы
  calcAmountInput.addEventListener('input', function() {
    const amount = parseInt(this.value) || 0;
    updateCalculatorDisplay(amount);
    updateBalanceCheck(amount);

    // Автоматически обновляем поле криптовалюты в форме вывода
    const withdrawalAmountInput = document.getElementById('withdrawal-amount');
    if (withdrawalAmountInput && document.activeElement !== withdrawalAmountInput) {
      withdrawalAmountInput.value = amount;
      updateCryptoAmountField();
    }
  });

  // Обработчики для выбора валюты (табы всегда активны)
  currencyTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const currency = this.dataset.currency;
      selectCurrencyTab(currency);

      // Обновляем калькулятор с текущей суммой
      const calcAmountInput = document.getElementById('calc-amount');
      if (calcAmountInput && calcAmountInput.value) {
        const amount = parseInt(calcAmountInput.value) || 0;
        updateCalculatorDisplay(amount);
      }
    });
  });

  // Первоначальный расчет для TON (активная по умолчанию) - показываем пустое состояние
  updateCalculatorDisplay(0);

  // Обновляем отображение комиссий и минимумов
  updateFeeDisplay();
  updateMinimumDisplay();

  // Запускаем автоматическое обновление статусов выплат
  startWithdrawalStatusMonitoring();
}

/**
 * Инициализирует табы валют
 */
function initializeCurrencyTabs() {
  // Обработчики событий уже добавлены в основной функции инициализации
  // Здесь можно добавить дополнительную логику инициализации табов при необходимости
}

/**
 * Выбирает таб валюты
 */
function selectCurrencyTab(currency) {
  const currencyTabs = document.querySelectorAll('.currency-tab');

  // Убираем активный класс со всех табов
  currencyTabs.forEach(tab => {
    tab.classList.remove('active');
  });

  // Добавляем активный класс к выбранному табу
  const selectedTab = document.querySelector(`[data-currency="${currency}"]`);
  if (selectedTab) {
    selectedTab.classList.add('active');
  }

  // Обновляем иконку и название в карточке валюты
  updateCurrencyCardIcon(currency);

  // Обновляем информацию о валюте (минимумы, комиссии, статус)
  updateCurrencyInfo(currency);
}

/**
 * Обновляет иконку и название в карточке валюты
 */
function updateCurrencyCardIcon(currency) {
  const cardIcon = document.getElementById('currency-card-icon');
  const cardName = document.getElementById('currency-card-name');

  if (!cardIcon || !cardName) return;

  // Определяем иконки и названия для каждой валюты
  const currencyIcons = {
    eth: {
      name: 'Ethereum (ETH)',
      icon: `
        <path d="M12 2L6 12.5l6 3.5 6-3.5L12 2z" opacity="0.8"/>
        <path d="M12 16L6 12.5l6 9.5 6-9.5L12 16z" opacity="0.6"/>
        <path d="M12 2v6l5.5 2.5L12 2z" opacity="0.4"/>
        <path d="M12 8v8l5.5-3.5L12 8z" opacity="0.3"/>
      `
    },
    btc: {
      name: 'Bitcoin (BTC)',
      icon: `
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5"/>
        <path d="M8 12h8M8 8h6a2 2 0 0 1 0 4M8 16h6a2 2 0 0 0 0-4" fill="none" stroke="currentColor" stroke-width="1.5"/>
        <path d="M12 6v2M12 16v2" stroke="currentColor" stroke-width="1.5"/>
      `
    },
    usdttrc20: {
      name: 'USDT (TRC20)',
      icon: `
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5"/>
        <path d="M8 9h8v2H8z" fill="currentColor"/>
        <path d="M10 11v4h4v-4" fill="none" stroke="currentColor" stroke-width="1.5"/>
        <path d="M12 7v2M12 15v2" stroke="currentColor" stroke-width="1.5"/>
      `
    },
    ton: {
      name: 'TON (Telegram)',
      icon: `
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5"/>
        <path d="M8 8l8 8M16 8l-8 8" stroke="currentColor" stroke-width="1.5"/>
        <circle cx="12" cy="12" r="3" fill="currentColor" opacity="0.3"/>
      `
    }
  };

  const currencyData = currencyIcons[currency];
  if (currencyData) {
    cardIcon.innerHTML = currencyData.icon;
    cardName.textContent = currencyData.name;
  }
}

/**
 * Обновляет форму вывода с выбранной валютой
 */
function updateWithdrawalForm(currency, amount) {
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');
  const cryptoCurrencySelect = document.getElementById('crypto-currency');

  if (withdrawalAmountInput) {
    withdrawalAmountInput.value = amount;
  }

  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.value = currency;
  }

  // Обновляем расчет суммы к получению
  updateCryptoAmountField();

  // Обновляем placeholder адреса
  updateAddressPlaceholder();

  // Валидируем форму
  validateWithdrawalForm();
}

/**
 * Обновляет информацию о выбранной валюте
 */
function updateCurrencyInfo(currency) {
  const data = currencyData[currency];
  if (!data) return;

  // Обновляем иконку и название
  const currencyIcon = document.querySelector('.currency-icon');
  const currencyFullName = document.querySelector('.currency-full-name');
  const currencyBadge = document.querySelector('.currency-badge');

  // 🛡️ НАВСЕГДА ЗАБЛОКИРОВАНО! НЕ ТРОГАЕМ ИКОНКИ!
  // Эта функция постоянно ломала белые SVG иконки в карточках валют
  // НИКОГДА НЕ РАСКОММЕНТИРОВАТЬ ЭТУ ЧАСТЬ!
  /*
  if (currencyIcon) {
    const icons = {
      'eth': '⭐',
      'btc': '₿',
      'usdttrc20': '💲',
      'ton': '💎'
    };
    currencyIcon.textContent = icons[currency] || '💰';
  }
  */
  console.log(`[IconPreservation] 🛡️ НАВСЕГДА заблокирована замена SVG на эмодзи для ${currency}`);

  if (currencyFullName) {
    currencyFullName.textContent = data.name;
  }

  if (currencyBadge) {
    currencyBadge.className = `currency-badge status-${data.status}`;
    const statusTexts = {
      'best': window.appLocalization ? window.appLocalization.get('currency_status.best') : 'Лучший выбор',
      'good': window.appLocalization ? window.appLocalization.get('currency_status.good') : 'Хорошо',
      'expensive': window.appLocalization ? window.appLocalization.get('currency_status.expensive') : 'Дорого',
      'available': window.appLocalization ? window.appLocalization.get('currency_status.available') : 'Доступно'
    };
    currencyBadge.textContent = statusTexts[data.status] || 'Обычно';
  }

  // Обновляем требования в карточке валюты
  // Порядок: 0-Минимум, 1-Сумма к выводу, 2-Комиссия, 3-Получите, 4-Эффективность
  const requirementValues = document.querySelectorAll('.requirement-value');
  if (requirementValues.length >= 5) {
    const minCoins = data.minCoins;
    const minUsd = (minCoins * coinValue).toFixed(2);

    // 0: Минимум к выводу с переносом строки
    const coinsText = window.appLocalization ?
      window.appLocalization.get('currency.coins') :
      'монет';
    requirementValues[0].innerHTML = `${minCoins.toLocaleString()} ${coinsText}<br>($${minUsd})`;
    console.log(`[UI] Обновлен минимум в карточке валюты для ${currency}: ${minCoins} монет ($${minUsd})`);

    // 1: Сумма к выводу - будет обновляться в updateCurrencyCardDisplay
    // 2: Сетевая комиссия
    if (appSettings && appSettings.show_fees_to_user) {
      requirementValues[2].textContent = `$${data.networkFee}`;
    } else {
      requirementValues[2].textContent = 'Скрыто';
    }

    // 3: Вы получите - будет обновляться в updateCurrencyCardDisplay
    // 4: Эффективность - будет обновляться в updateCurrencyCardDisplay
  }
}

/**
 * Обновляет отображение калькулятора для выбранной валюты
 */
function updateCalculatorDisplay(coinAmount) {
  const dollarAmount = coinAmount * 0.001; // 1 монета = $0.001
  const dollarEquivalent = document.getElementById('dollar-equivalent');
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');

  if (dollarEquivalent) {
    dollarEquivalent.textContent = `= $${formatCurrency(dollarAmount)}`;
  }

  // Получаем активную валюту
  const activeTab = document.querySelector('.currency-tab.active');
  if (!activeTab) return;

  const currency = activeTab.dataset.currency;
  const data = currencyData[currency];
  if (!data) return;

  // Обновляем расчеты для выбранной валюты с форматированием
  updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount);

  // Обновляем карточку валют
  updateCurrencyCardDisplay(currency, coinAmount, dollarAmount);

  // Обновляем статус табов (доступность)
  updateCurrencyTabsStatus(coinAmount);

  // ИСПРАВЛЕНИЕ: НЕ вызываем updateCurrencyCalculation для активной валюты,
  // так как она уже обновлена через updateCurrencyCalculationDisplay с реальными данными API
  
  // Обновляем сумму в форме вывода (только если поле не в фокусе)
  if (withdrawalAmountInput && document.activeElement !== withdrawalAmountInput) {
    withdrawalAmountInput.value = coinAmount;
    updateCryptoAmountField();
  }
}

/**
 * Обновляет расчеты для конкретной валюты в новом интерфейсе
 */
function updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount) {
  const data = currencyData[currency];
  if (!data) return;

  // ИСПРАВЛЕНИЕ: Используем правильные селекторы для новой структуры карточек
  const requirementValues = document.querySelectorAll('.requirement-value');
  const withdrawalAmountDisplay = requirementValues[1]; // Индекс 1: Сумма к выводу
  const feeAmountDisplay = requirementValues[2]; // Индекс 2: Сетевая комиссия
  const finalAmountDisplay = requirementValues[3]; // Индекс 3: Вы получите
  const efficiencyDisplay = requirementValues[4]; // Индекс 4: Эффективность
  const actionStatus = document.getElementById('action-status');

  const userBalance = currentUserBalance || 0;

  // Проверяем различные условия
  if (coinAmount === 0) {
    // Нет суммы
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = '-';
    if (feeAmountDisplay) feeAmountDisplay.textContent = '-';
    if (finalAmountDisplay) finalAmountDisplay.textContent = '-';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card neutral';
      actionStatus.querySelector('.status-icon').textContent = '💡';
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.enter_coins_amount') : 'Введите сумму для расчета';
    }

  } else if (coinAmount > userBalance) {
    // Недостаточно средств на балансе
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(2)}`;
    if (feeAmountDisplay) {
      if (appSettings.show_fees_to_user) {
        feeAmountDisplay.textContent = `$${data.networkFee}`;
        feeAmountDisplay.style.opacity = '1';
      } else {
        feeAmountDisplay.textContent = 'Скрыто';
        feeAmountDisplay.style.opacity = '0.6';
      }
    }
    if (finalAmountDisplay) finalAmountDisplay.textContent = 'Недостаточно средств';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card insufficient-funds';
      actionStatus.querySelector('.status-icon').textContent = '❌';
      const needAmount = coinAmount - userBalance;
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds_need', {amount: needAmount}) :
        `Недостаточно средств! Нужно: ${needAmount} монет`;
    }

  } else if (coinAmount < data.minCoins) {
    // Меньше минимума
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(2)}`;
    if (feeAmountDisplay) {
      if (appSettings.show_fees_to_user) {
        feeAmountDisplay.textContent = `$${data.networkFee}`;
        feeAmountDisplay.style.opacity = '1';
      } else {
        feeAmountDisplay.textContent = 'Скрыто';
        feeAmountDisplay.style.opacity = '0.6';
      }
    }
    if (finalAmountDisplay) {
      const belowMinText = window.appLocalization ?
        window.appLocalization.get('earnings.below_minimum') :
        'Меньше минимума';
      finalAmountDisplay.textContent = belowMinText;
    }
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card insufficient-minimum';
      actionStatus.querySelector('.status-icon').textContent = '⚠️';
      const minCoinsFormatted = data.minCoins.toLocaleString();
      const minUsd = (data.minCoins * 0.001).toFixed(2);
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.minimum_amount', {amount: minCoinsFormatted, usd: `$${minUsd}`}) :
        `Минимум: ${minCoinsFormatted} монет ($${minUsd})`;
    }

  } else {
    // Достаточно для вывода - используем единый калькулятор для точного расчета
    updateCurrencyCalculationWithUnifiedCalculator(currency, coinAmount, dollarAmount,
      withdrawalAmountDisplay, feeAmountDisplay, finalAmountDisplay, efficiencyDisplay, actionStatus);
  }
}

/**
 * Обновляет отображение в карточке валют
 * Порядок элементов: 0-Минимум, 1-Сумма к выводу, 2-Комиссия, 3-Получите, 4-Эффективность
 */
function updateCurrencyCardDisplay(currency, coinAmount, dollarAmount) {
  const data = currencyData[currency];
  if (!data) return;

  // Получаем элементы карточки валют
  const requirementValues = document.querySelectorAll('.requirement-value');
  if (requirementValues.length < 5) return;

  const userBalance = currentUserBalance || 0;

  // Обновляем "Сумма к выводу" (индекс 1)
  if (coinAmount === 0) {
    requirementValues[1].textContent = '-';
  } else {
    requirementValues[1].textContent = `$${dollarAmount.toFixed(2)}`;
  }

  // Обновляем "Вы получите" (индекс 3) и "Эффективность" (индекс 4)
  if (coinAmount === 0) {
    requirementValues[3].textContent = '-';
    requirementValues[4].textContent = '-';
  } else if (coinAmount > userBalance) {
    const insufficientText = window.appLocalization ?
      window.appLocalization.get('earnings.insufficient_funds') :
      'Недостаточно средств';
    requirementValues[3].textContent = insufficientText;
    requirementValues[4].textContent = '-';
  } else if (coinAmount < data.minCoins) {
    const belowMinText = window.appLocalization ?
      window.appLocalization.get('earnings.below_minimum') :
      'Меньше минимума';
    requirementValues[3].textContent = belowMinText;
    requirementValues[4].textContent = '-';
  } else {
    // Достаточно для вывода - рассчитываем реальные значения
    updateCurrencyCardWithCalculation(currency, coinAmount, dollarAmount, requirementValues);
  }
}

/**
 * Обновляет карточку валют с реальными расчетами
 */
async function updateCurrencyCardWithCalculation(currency, coinAmount, dollarAmount, requirementValues) {
  try {
    const result = await calculateCryptoAmount(coinAmount, currency, false);

    if (result.status === "success") {
      const cryptoAmountNet = parseFloat(result.raw_amount || result.amount);
      const usdAmount = parseFloat(result.usd_amount || dollarAmount);
      let networkFeeUsd = parseFloat(result.fee_details?.fee_usd || 0);

      // Если комиссия в USD не указана, используем статические данные
      if (networkFeeUsd === 0) {
        const data = currencyData[currency];
        if (data && data.networkFee > 0) {
          networkFeeUsd = data.networkFee;
        }
      }

      const afterFeeUsd = usdAmount - networkFeeUsd;
      const efficiency = afterFeeUsd > 0 ? ((afterFeeUsd / dollarAmount) * 100) : 0;

      // Получаем префикс валюты для отображения
      const currencyPrefix = getCurrencyPrefix(currency);

      // Обновляем "Вы получите" с префиксом валюты и значением в крипте
      if (afterFeeUsd > 0) {
        requirementValues[3].textContent = `$${afterFeeUsd.toFixed(2)} (${currencyPrefix}${cryptoAmountNet.toFixed(8)})`;
      } else {
        requirementValues[3].textContent = 'Убыток';
      }

      // Обновляем "Эффективность"
      requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;

    } else {
      // Fallback на старую логику
      const data = currencyData[currency];
      const afterFee = dollarAmount - data.networkFee;
      const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

      if (afterFee > 0) {
        requirementValues[3].textContent = `$${afterFee.toFixed(2)}`;
        requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;
      } else {
        requirementValues[3].textContent = 'Убыток';
        requirementValues[4].textContent = '0%';
      }
    }

  } catch (error) {
    console.warn('Ошибка расчета для карточки валют:', error);
    // Fallback на старую логику
    const data = currencyData[currency];
    const afterFee = dollarAmount - data.networkFee;
    const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

    if (afterFee > 0) {
      requirementValues[3].textContent = `$${afterFee.toFixed(2)}`;
      requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;
    } else {
      requirementValues[3].textContent = 'Убыток';
      requirementValues[4].textContent = '0%';
    }
  }
}

/**
 * Получает префикс валюты для отображения
 */
function getCurrencyPrefix(currency) {
  const prefixes = {
    'ton': 'TON ',
    'eth': 'ETH ',
    'btc': 'BTC ',
    'usdttrc20': 'USDT ',
    'ltc': 'LTC ',
    'bch': 'BCH ',
    'xrp': 'XRP ',
    'ada': 'ADA ',
    'dot': 'DOT '
  };
  return prefixes[currency.toLowerCase()] || currency.toUpperCase() + ' ';
}

/**
 * Обновляет расчеты валюты с использованием единого калькулятора комиссий
 */
async function updateCurrencyCalculationWithUnifiedCalculator(currency, coinAmount, dollarAmount,
  withdrawalAmountDisplay, feeAmountDisplay, finalAmountDisplay, efficiencyDisplay, actionStatus) {

  try {
    // ИСПРАВЛЕНИЕ: Используем тот же серверный расчет что и форма выплат
    const result = await calculateCryptoAmount(coinAmount, currency, false);

    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(2)}`;

    if (result.status === "success") {
      // ИСПРАВЛЕНИЕ: Используем правильные поля из нового API
      const cryptoAmountNet = parseFloat(result.raw_amount || result.amount); // Сумма ПОСЛЕ вычета комиссии
      const cryptoAmountGross = parseFloat(result.crypto_amount_gross || cryptoAmountNet); // Сумма ДО вычета комиссии
      const nowPaymentsFee = parseFloat(result.nowpayments_fee || 0); // Комиссия в криптовалюте
      const usdAmount = parseFloat(result.usd_amount || dollarAmount);
      let networkFeeUsd = parseFloat(result.fee_details?.fee_usd || 0); // Комиссия в USD

      // ИСПРАВЛЕНИЕ: Если комиссия в USD не указана, рассчитываем её из разности
      if (networkFeeUsd === 0 && cryptoAmountGross > cryptoAmountNet) {
        // Рассчитываем комиссию в USD на основе разности криптовалют
        const feeRatio = (cryptoAmountGross - cryptoAmountNet) / cryptoAmountGross;
        networkFeeUsd = dollarAmount * feeRatio;
        console.log(`[Calculator] Рассчитана комиссия из разности крипто: ${networkFeeUsd.toFixed(4)} USD (${(feeRatio * 100).toFixed(2)}%)`);
      }

      // ДОПОЛНИТЕЛЬНОЕ ИСПРАВЛЕНИЕ: Если комиссия все еще 0, используем статические данные
      if (networkFeeUsd === 0) {
        const data = currencyData[currency];
        if (data && data.networkFee > 0) {
          networkFeeUsd = data.networkFee;
          console.log(`[Calculator] Используем статическую комиссию из currencyData: ${networkFeeUsd} USD`);
        }
      }

      console.log(`[Calculator] Данные для ${currency}: Gross=${cryptoAmountGross}, Net=${cryptoAmountNet}, Fee=${nowPaymentsFee}, FeeUSD=${networkFeeUsd}`);

      // Показываем реальную комиссию NOWPayments
      if (feeAmountDisplay) {
        if (networkFeeUsd > 0) {
          // Используем комиссию в USD из API
          feeAmountDisplay.textContent = `$${networkFeeUsd.toFixed(2)}`;
          feeAmountDisplay.style.opacity = '1';
        } else if (nowPaymentsFee > 0) {
          // Fallback: конвертируем комиссию в криптовалюте в USD
          const feeInUsd = nowPaymentsFee * (usdAmount / cryptoAmountGross);
          feeAmountDisplay.textContent = `$${feeInUsd.toFixed(2)}`;
          feeAmountDisplay.style.opacity = '1';
        } else {
          feeAmountDisplay.textContent = 'Расчет...';
          feeAmountDisplay.style.opacity = '0.6';
        }
      }

      // ИСПРАВЛЕНИЕ: Рассчитываем эффективность правильно
      const afterFeeUsd = usdAmount - networkFeeUsd; // USD после вычета комиссии
      const efficiency = afterFeeUsd > 0 ? ((afterFeeUsd / dollarAmount) * 100) : 0;

      console.log(`[Calculator] ${currency}: Исходная сумма=$${dollarAmount.toFixed(2)}, После комиссии=$${afterFeeUsd.toFixed(2)}, Эффективность=${efficiency.toFixed(1)}%`);

      if (finalAmountDisplay) {
        finalAmountDisplay.textContent = `$${afterFeeUsd.toFixed(2)}`;
        console.log(`[Calculator] Обновлено final-amount-display: $${afterFeeUsd.toFixed(2)}`);
      }
      if (efficiencyDisplay) {
        efficiencyDisplay.textContent = `${efficiency.toFixed(1)}%`;
        console.log(`[Calculator] Обновлено efficiency-display: ${efficiency.toFixed(1)}%`);
      }

      if (actionStatus) {
        actionStatus.className = 'action-status-card available';
        actionStatus.querySelector('.status-icon').textContent = '✅';
        actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
          window.appLocalization.get('earnings.ready_for_withdrawal', {amount: `$${afterFeeUsd.toFixed(2)}`}) :
          `Готово к выводу! Получите $${afterFeeUsd.toFixed(2)}`;
      }

    } else if (result.status === "fee_exceeds_amount") {
      // Комиссия больше суммы
      if (feeAmountDisplay) {
        feeAmountDisplay.textContent = 'Высокая';
        feeAmountDisplay.style.opacity = '1';
      }
      if (finalAmountDisplay) finalAmountDisplay.textContent = 'Убыток';
      if (efficiencyDisplay) efficiencyDisplay.textContent = '0%';

      if (actionStatus) {
        actionStatus.className = 'action-status-card loss';
        actionStatus.querySelector('.status-icon').textContent = '💸';
        actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
          window.appLocalization.get('earnings.fee_exceeds_amount') :
          'Комиссия больше суммы вывода';
      }

    } else {
      // Другие ошибки - fallback на старую логику
      fallbackToOldCalculation(currency, coinAmount, dollarAmount,
        withdrawalAmountDisplay, feeAmountDisplay, finalAmountDisplay, efficiencyDisplay, actionStatus);
    }

  } catch (error) {
    console.warn('Ошибка единого калькулятора, используем fallback:', error);
    // Fallback на старую логику при ошибке
    fallbackToOldCalculation(currency, coinAmount, dollarAmount,
      withdrawalAmountDisplay, feeAmountDisplay, finalAmountDisplay, efficiencyDisplay, actionStatus);
  }
}

/**
 * Fallback функция со старой логикой расчета
 */
function fallbackToOldCalculation(currency, coinAmount, dollarAmount,
  withdrawalAmountDisplay, feeAmountDisplay, finalAmountDisplay, efficiencyDisplay, actionStatus) {

  console.warn(`[Calculator] Используем fallback расчет для ${currency} (старая логика)`);

  const data = currencyData[currency];
  if (!data) return;

  // ИСПРАВЛЕНИЕ: Комиссия ВСЕГДА вычитается для правильного расчета эффективности
  // Настройка show_fees_to_user влияет только на отображение, не на расчеты
  const afterFee = dollarAmount - data.networkFee;
  const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

  console.log(`[Calculator Fallback] ${currency}: Исходная сумма=$${dollarAmount.toFixed(2)}, Комиссия=$${data.networkFee}, После комиссии=$${afterFee.toFixed(2)}, Эффективность=${efficiency.toFixed(1)}%`);
  console.log(`🔧 ИСПРАВЛЕНИЕ ПРИМЕНЕНО: Комиссия всегда вычитается для расчета эффективности`);

  if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(2)}`;
  if (feeAmountDisplay) {
    if (appSettings && appSettings.show_fees_to_user) {
      feeAmountDisplay.textContent = `$${data.networkFee}`;
      if (feeAmountDisplay.style) feeAmountDisplay.style.opacity = '1';
    } else {
      feeAmountDisplay.textContent = 'Скрыто';
      if (feeAmountDisplay.style) feeAmountDisplay.style.opacity = '0.6';
    }
  }

  if (afterFee > 0) {
    if (finalAmountDisplay) finalAmountDisplay.textContent = `$${afterFee.toFixed(2)}`;
    if (efficiencyDisplay) efficiencyDisplay.textContent = `${efficiency.toFixed(1)}%`;

    if (actionStatus) {
      actionStatus.className = 'action-status-card available';
      actionStatus.querySelector('.status-icon').textContent = '✅';
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.ready_for_withdrawal', {amount: `$${afterFee.toFixed(2)}`}) :
        `Готово к выводу! Получите $${afterFee.toFixed(2)}`;
    }
  } else {
    if (finalAmountDisplay) finalAmountDisplay.textContent = 'Убыток';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '0%';

    if (actionStatus) {
      actionStatus.className = 'action-status-card loss';
      actionStatus.querySelector('.status-icon').textContent = '💸';
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.fee_exceeds_amount') :
        'Комиссия больше суммы вывода';
    }
  }
}

/**
 * Обновляет статус табов валют (табы всегда активны, только визуальная индикация)
 */
function updateCurrencyTabsStatus(coinAmount) {
  const currencyTabs = document.querySelectorAll('.currency-tab');
  const userBalance = currentUserBalance || 0;

  currencyTabs.forEach(tab => {
    const currency = tab.dataset.currency;
    const data = currencyData[currency];

    if (!data) return;

    // Убираем все классы статуса
    tab.classList.remove('insufficient', 'warning', 'available');

    // Добавляем визуальную индикацию (но табы остаются кликабельными)
    if (coinAmount > userBalance) {
      tab.classList.add('insufficient'); // Красная индикация
    } else if (coinAmount > 0 && coinAmount < data.minCoins) {
      tab.classList.add('warning'); // Оранжевая индикация
    } else if (coinAmount >= data.minCoins) {
      tab.classList.add('available'); // Зеленая индикация
    }
  });
}

/**
 * Обновляет расчет для конкретной валюты
 */
function updateCurrencyCalculation(currency, coinAmount, dollarAmount) {
  const data = currencyData[currency];
  const calcElement = document.getElementById(`calc-${currency}`);
  const statusElement = document.getElementById(`status-${currency}`);
  const currencyCard = document.querySelector(`[data-currency="${currency}"]`);

  if (!calcElement || !data) return;

  const amountSpan = calcElement.querySelector('.amount');
  const percentSpan = calcElement.querySelector('.percent');

  // Сброс классов
  if (currencyCard) {
    currencyCard.classList.remove('insufficient', 'selected');
  }

  // Проверяем баланс пользователя
  const userBalance = currentUserBalance || 0;

  if (coinAmount > userBalance) {
    // Недостаточно средств на балансе
    amountSpan.textContent = 'Недостаточно средств';
    percentSpan.textContent = `Нужно: ${coinAmount - userBalance} монет`;

    if (statusElement) {
      statusElement.textContent = 'Недостаточно средств';
      statusElement.className = 'action-status insufficient-funds';
    }

    if (currencyCard) {
      currencyCard.classList.add('insufficient');
    }

  } else if (coinAmount < data.minCoins) {
    // Недостаточно для минимума
    amountSpan.textContent = `Мин: ${data.minCoins.toLocaleString()} монет`;
    percentSpan.textContent = `($${(data.minCoins * 0.001).toFixed(2)})`;

    if (statusElement) {
      statusElement.textContent = `Минимум: ${data.minCoins.toLocaleString()} монет`;
      statusElement.className = 'action-status insufficient-minimum';
    }

    if (currencyCard) {
      currencyCard.classList.add('insufficient');
    }

  } else {
    // Достаточно для вывода
    // ИСПРАВЛЕНИЕ: Комиссия ВСЕГДА вычитается для правильного расчета эффективности
    const afterFee = dollarAmount - data.networkFee;
    const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

    if (afterFee > 0) {
      amountSpan.textContent = `$${afterFee.toFixed(2)}`;
      percentSpan.textContent = `Эффективность: ${efficiency.toFixed(1)}%`;

      if (statusElement) {
        statusElement.textContent = '✅ Доступно для вывода';
        statusElement.className = 'action-status available';
      }

      if (currencyCard) {
        currencyCard.classList.remove('insufficient');
      }

    } else {
      amountSpan.textContent = 'Убыток';
      percentSpan.textContent = `Комиссия больше суммы`;

      if (statusElement) {
        statusElement.textContent = '⚠️ Комиссия больше суммы';
        statusElement.className = 'action-status loss';
      }

      if (currencyCard) {
        currencyCard.classList.add('insufficient');
      }
    }
  }
}

/**
 * Обновляет проверку баланса
 */
function updateBalanceCheck(amount) {
  const balanceCheck = document.getElementById('balance-check');
  const calcBalance = document.getElementById('calc-balance');

  // Обновляем баланс в заголовке калькулятора
  if (calcBalance) {
    calcBalance.textContent = `${currentUserBalance || 0} монет`;
  }

  if (balanceCheck) {
    const userBalance = currentUserBalance || 0;

    if (amount === 0) {
      balanceCheck.textContent = 'Введите сумму';
      balanceCheck.className = 'balance-status neutral';
    } else if (amount > userBalance) {
      balanceCheck.textContent = `Недостаточно! Доступно: ${userBalance} монет`;
      balanceCheck.className = 'balance-status insufficient';
    } else {
      balanceCheck.textContent = `Достаточно средств (${userBalance} монет)`;
      balanceCheck.className = 'balance-status sufficient';
    }
  }
}

/**
 * Выбирает валюту и обновляет форму вывода
 */
function selectCurrency(currency) {
  const currencyCards = document.querySelectorAll('.currency-card');
  const calcAmountInput = document.getElementById('calc-amount');
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');
  const cryptoCurrencySelect = document.getElementById('crypto-currency');

  // Убираем выделение со всех валют
  currencyCards.forEach(card => {
    card.classList.remove('selected');
  });

  // Выделяем выбранную валюту
  const selectedCard = document.querySelector(`[data-currency="${currency}"]`);
  if (selectedCard && !selectedCard.classList.contains('insufficient')) {
    selectedCard.classList.add('selected');

    // Обновляем форму вывода
    const amount = parseInt(calcAmountInput?.value) || 0;

    if (withdrawalAmountInput) {
      // Обновляем только если поле не в фокусе
      if (document.activeElement !== withdrawalAmountInput) {
        withdrawalAmountInput.value = amount;
      }
    }

    if (cryptoCurrencySelect) {
      cryptoCurrencySelect.value = currency;
    }

    // Обновляем расчет суммы к получению
    updateCryptoAmountField();

    // Обновляем placeholder адреса
    updateAddressPlaceholder();

    // Валидируем форму
    validateWithdrawalForm();

    // Показываем уведомление
    showCurrencySelectedNotification(currency);
  }
}

/**
 * Показывает уведомление о выборе валюты
 */
function showCurrencySelectedNotification(currency) {
  const data = currencyData[currency];
  if (!data) return;

  // Создаем временное уведомление
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--app-primary-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease;
  `;
  notification.textContent = `✅ Выбрана валюта: ${data.name}`;

  document.body.appendChild(notification);

  // Удаляем через 3 секунды
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}













// --- Функции для работы с историей выплат ---

/** Отображает историю выплат */
function displayWithdrawalHistory(withdrawals) {
  const historyContainer = document.querySelector('.placeholder-list');
  if (!historyContainer) {
    console.warn("Не найден контейнер для истории выплат");
    return;
  }

  // Очищаем контейнер
  historyContainer.innerHTML = "";

  if (withdrawals && withdrawals.length > 0) {
    // Создаем список выплат
    withdrawals.forEach(withdrawal => {
      const withdrawalItem = document.createElement("div");
      withdrawalItem.className = "withdrawal-item";

      // Обрабатываем дату (может быть timestamp или строка)
      let date;
      if (withdrawal.timestamp) {
        date = new Date(withdrawal.timestamp * 1000);
      } else if (withdrawal.created_at) {
        date = new Date(withdrawal.created_at);
      } else {
        date = new Date();
      }
      const dateStr = date.toLocaleDateString('ru-RU');
      const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

      // Определяем статус на русском
      const statusText = getWithdrawalStatusText(withdrawal.status);
      let statusClass = "";

      // Определяем CSS класс на основе статуса
      switch (withdrawal.status) {
        case 'waiting':
        case 'pending':
          statusClass = "status-pending";
          break;
        case 'processing':
        case 'sending':
          statusClass = "status-processing";
          break;
        case 'completed':
        case 'finished':
        case 'confirmed':
          statusClass = "status-completed";
          break;
        case 'failed':
        case 'cancelled':
        case 'rejected':
          statusClass = "status-failed";
          break;
        case 'expired':
          statusClass = "status-expired";
          break;
        default:
          statusClass = "status-unknown";
      }

      // Добавляем кнопку отмены для платежей в обработке
      const canCancel = ['waiting', 'pending', 'processing', 'sending'].includes(withdrawal.status);
      const cancelButton = canCancel ? `
        <div class="withdrawal-actions">
          <button class="cancel-button" onclick="cancelWithdrawal('${withdrawal.id}')">
            Отменить
          </button>
        </div>
      ` : '';

      // Формируем дополнительную информацию для завершенных выплат
      let additionalInfo = '';
      if (['finished', 'completed', 'confirmed'].includes(withdrawal.status)) {
        additionalInfo = '<div class="withdrawal-success-info">✅ Средства отправлены на ваш кошелек</div>';

        // Добавляем ссылку на блокчейн если есть hash транзакции
        if (withdrawal.transaction_hash || withdrawal.txid) {
          const hash = withdrawal.transaction_hash || withdrawal.txid;
          let blockchainUrl = '';

          switch (withdrawal.currency.toLowerCase()) {
            case 'eth':
              blockchainUrl = `https://etherscan.io/tx/${hash}`;
              break;
            case 'btc':
              blockchainUrl = `https://blockstream.info/tx/${hash}`;
              break;
            case 'usdttrc20':
            case 'trx':
              blockchainUrl = `https://tronscan.org/#/transaction/${hash}`;
              break;
            default:
              blockchainUrl = `https://etherscan.io/tx/${hash}`;
          }

          additionalInfo += `<div class="withdrawal-blockchain-link">
            <a href="${blockchainUrl}" target="_blank" rel="noopener">
              🔗 Посмотреть в блокчейне
            </a>
          </div>`;
        }
      } else if (['failed', 'rejected'].includes(withdrawal.status)) {
        additionalInfo = '<div class="withdrawal-error-info">❌ Выплата не удалась, средства возвращены</div>';
      } else if (['waiting', 'processing', 'sending'].includes(withdrawal.status)) {
        additionalInfo = '<div class="withdrawal-pending-info">⏳ Выплата обрабатывается, ожидайте</div>';
      }

      withdrawalItem.innerHTML = `
        <div class="withdrawal-header">
          <span class="withdrawal-amount">${withdrawal.coins_amount} ${window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет'}</span>
          <span class="withdrawal-status ${statusClass}">${statusText}</span>
        </div>
        <div class="withdrawal-details">
          <div class="withdrawal-currency">${withdrawal.currency.toUpperCase()}</div>
          <div class="withdrawal-date">${dateStr} ${timeStr}</div>
          ${withdrawal.payout_id ? `<div class="withdrawal-payout-id">ID: ${withdrawal.payout_id}</div>` : ''}
        </div>
        <div class="withdrawal-address">${withdrawal.wallet_address || withdrawal.address || 'Адрес не указан'}</div>
        ${additionalInfo}
        ${cancelButton}
      `;

      historyContainer.appendChild(withdrawalItem);
    });
  } else {
    showEmptyHistory();
  }
}

/** Показывает пустую историю с красивым дизайном */
function showEmptyHistory() {
  const historyContainer = document.querySelector('.placeholder-list') || document.querySelector('#withdrawal-history-list');
  if (!historyContainer) return;

  historyContainer.innerHTML = `
    <div class="empty-history-state">
      <div class="empty-history-icon">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
          <path d="M12 2v10"/>
          <path d="M8 8l4-4 4 4"/>
          <rect x="3" y="14" width="18" height="6" rx="2"/>
          <circle cx="7" cy="17" r="1"/>
          <circle cx="17" cy="17" r="1"/>
        </svg>
      </div>
      <div class="empty-history-content">
        <h4 class="empty-history-title">История выплат пуста</h4>
        <p class="empty-history-description">
          Ваши запросы на вывод будут отображаться здесь
        </p>
        <div class="empty-history-hint">
          <span class="hint-icon">💡</span>
          <span>Перейдите в калькулятор, чтобы рассчитать и запросить первый вывод</span>
        </div>
      </div>
    </div>
  `;
}

// Функция загрузки и отображения истории выплат
async function loadAndDisplayWithdrawalHistory() {
    console.log('[Withdrawal History] Загружаем историю выплат');

    const userData = getUserDataForAPI();
    console.log('[Withdrawal History] Данные пользователя:', userData);

    if (!userData) {
        console.log('[Withdrawal History] Нет данных пользователя - используем принудительную загрузку');
        // ПРИНУДИТЕЛЬНАЯ ЗАГРУЗКА для тестирования
        userData = { initData: 'fallback_test' };
    }

    // Сначала принудительно обновляем статусы
    console.log('[Withdrawal History] Обновляем статусы перед загрузкой истории');
    await forceUpdateWithdrawalStatuses();

    try {
        const response = await fetch(`${API_BASE_URL}/getWithdrawalHistory.php`, {
            method: "POST",
            headers: {"Content-Type": "application/json"},
            body: JSON.stringify(userData)
        });

        if (!response.ok) {
            throw new Error('Не удалось загрузить историю выплат');
        }

        const data = await response.json();
        console.log('[Withdrawal History] Получены данные:', data);
        console.log('[Withdrawal History] Количество выплат:', data.withdrawals ? data.withdrawals.length : 0);

        if (data.withdrawals && data.withdrawals.length > 0) {
            console.log('[Withdrawal History] Отображаем', data.withdrawals.length, 'выплат');
            displayWithdrawalHistory(data.withdrawals);
        } else {
            console.log('[Withdrawal History] Нет выплат для отображения');
            console.log('[Withdrawal History] Данные ответа:', JSON.stringify(data));
            showEmptyHistory();
        }
    } catch (error) {
        console.error('[Withdrawal History] Ошибка загрузки истории выплат:', error);
        showStatus('Ошибка загрузки истории выплат', 'error');
        showEmptyHistory();
    }
}

// Обновить историю выплат после создания новой выплаты
function updateHistoryAfterWithdrawal() {
    loadAndDisplayWithdrawalHistory();
}

// Принудительная загрузка истории (для кнопки в интерфейсе)
function forceLoadHistory() {
    console.log('[Force Load] Принудительная загрузка истории выплат');

    // Показываем индикатор загрузки
    const historyContainer = document.querySelector('.placeholder-list') || document.querySelector('#withdrawal-history-list');
    if (historyContainer) {
        historyContainer.innerHTML = '<div class="history-item placeholder">🔄 Загрузка истории выплат...</div>';
    }

    // Принудительно загружаем историю
    loadAndDisplayWithdrawalHistory();

    // Дополнительно через 2 секунды
    setTimeout(() => {
        console.log('[Force Load] Повторная загрузка через 2 секунды');
        loadAndDisplayWithdrawalHistory();
    }, 2000);
}

/** Проверяет и обновляет статусы выплат пользователя */
async function checkAndUpdateWithdrawalStatuses() {
    try {
        const response = await fetch(`${API_BASE_URL}/checkUserWithdrawals.php`, {
            method: "POST",
            headers: {"Content-Type": "application/json"},
            body: JSON.stringify({initData: tg.initData})
        });

        if (!response.ok) {
            throw new Error('Не удалось проверить статусы выплат');
        }

        const data = await response.json();
        if (data.success) {
            console.log('Проверка статусов завершена:', data.message);

            // Если есть обновления, показываем уведомления
            if (data.status_changes && data.status_changes.length > 0) {
                data.status_changes.forEach(change => {
                    const statusText = getWithdrawalStatusText(change.new_status);
                    const message = `Выплата ${change.amount} монет (${change.currency.toUpperCase()}): ${statusText}`;

                    if (['completed', 'finished', 'confirmed'].includes(change.new_status)) {
                        showStatus(message, 'success');
                    } else if (['failed', 'cancelled', 'rejected'].includes(change.new_status)) {
                        showStatus(message, 'error');
                    } else {
                        showStatus(message, 'info');
                    }
                });
            }

            // Обновляем отображение истории
            if (data.updated_count > 0) {
                loadAndDisplayWithdrawalHistory();
            }

            return data;
        } else {
            console.warn('Ошибка проверки статусов:', data.error);
            return null;
        }
    } catch (error) {
        console.error('Ошибка при проверке статусов выплат:', error);
        return null;
    }
}

/** Получает текст статуса выплаты на русском языке */
function getWithdrawalStatusText(status) {
    const statusMap = {
        // Статусы NOWPayments с понятными объяснениями
        'waiting': 'Ожидание обработки',
        'processing': 'Обрабатывается',
        'sending': 'Отправляется на кошелек',
        'finished': 'Отправлено на кошелек',
        'completed': 'Отправлено на кошелек',
        'confirmed': 'Подтверждено в блокчейне',
        'failed': 'Ошибка выплаты',
        'rejected': 'Отклонено системой',
        // Дополнительные статусы
        'pending': 'В обработке',
        'cancelled': 'Отменено',
        'expired': 'Истекло'
    };
    return statusMap[status] || status || 'Неизвестно';
}

/** Автоматически проверяет статусы выплат каждые 30 секунд */
function startWithdrawalStatusMonitoring() {
    console.log('[Withdrawal Monitor] Запуск мониторинга статусов выплат');

    // Проверяем сразу при запуске
    checkWithdrawalStatusUpdates();

    // Затем проверяем каждые 30 секунд
    setInterval(() => {
        checkWithdrawalStatusUpdates();
    }, 30000);

    // Дополнительно проверяем каждые 5 минут через принудительное обновление
    setInterval(() => {
        console.log('[Withdrawal Monitor] Принудительное обновление статусов через API');
        forceUpdateWithdrawalStatuses();
    }, 300000); // 5 минут
}

/** Принудительно обновляет статусы выплат через API */
async function forceUpdateWithdrawalStatuses() {
    try {
        console.log('[Withdrawal Monitor] Отправляем запрос на принудительное обновление статусов');

        const response = await fetch(`${API_BASE_URL}/force_update_withdrawals.php?json=1`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log(`[Withdrawal Monitor] Проверено: ${data.checked}, обновлено: ${data.updated}`);

                if (data.updated > 0) {
                    // Перезагружаем историю выплат
                    setTimeout(() => {
                        loadAndDisplayWithdrawalHistory();
                    }, 1000);

                    // Показываем уведомление пользователю
                    showStatus(`Обновлено статусов выплат: ${data.updated}`, 'success');
                }

                return data.updated;
            } else {
                console.error('[Withdrawal Monitor] Ошибка API:', data.error);
                return 0;
            }
        } else {
            console.error('[Withdrawal Monitor] Ошибка HTTP:', response.status);
            return 0;
        }
    } catch (error) {
        console.error('[Withdrawal Monitor] Ошибка принудительного обновления:', error);
        return 0;
    }
}

console.log("main.js загружен.");

// 🔧 ПРИНУДИТЕЛЬНОЕ ИСПРАВЛЕНИЕ ЛЕЙБЛОВ В КАРТОЧКЕ ВАЛЮТ
// Добавляем обработчик для исправления лейблов после полной загрузки DOM
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    console.log('🔧 Принудительное исправление лейблов карточки валют...');

    // Исправляем лейбл "Сумма к выводу" (индекс 1)
    const withdrawalLabel = document.getElementById('withdrawal-amount-label');
    if (withdrawalLabel && withdrawalLabel.textContent !== 'Сумма к выводу:') {
      withdrawalLabel.textContent = 'Сумма к выводу:';
      console.log('✅ Исправлен лейбл "Сумма к выводу"');
    }

    // Исправляем лейбл "Сетевая комиссия" (индекс 2)
    const networkFeeLabel = document.getElementById('network-fee-label');
    if (networkFeeLabel && networkFeeLabel.textContent !== 'Сетевая комиссия:') {
      networkFeeLabel.textContent = 'Сетевая комиссия:';
      console.log('✅ Исправлен лейбл "Сетевая комиссия"');
    }

    // Проверяем все лейблы в правильном порядке
    const requirementLabels = document.querySelectorAll('.requirement-label');
    const correctLabels = [
      'Минимум к выводу:',
      'Сумма к выводу:',
      'Сетевая комиссия:',
      'Вы получите:',
      'Эффективность:'
    ];

    requirementLabels.forEach((label, index) => {
      if (index < correctLabels.length && label.textContent !== correctLabels[index]) {
        label.textContent = correctLabels[index];
        console.log(`✅ Исправлен лейбл ${index}: "${correctLabels[index]}"`);
      }
    });

    console.log('🎯 Принудительное исправление лейблов завершено!');
  }, 2000); // Задержка 2 секунды для полной загрузки
});
