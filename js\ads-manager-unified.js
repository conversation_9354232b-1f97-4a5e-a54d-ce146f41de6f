/**
 * js/ads-manager-unified.js
 * Единый модуль управления рекламной системой
 * Объединяет функциональность ads-manager-full.js, ad-counters.js и server-ad-counters.js
 */

class AdsManagerUnified {
  constructor() {
    console.log('[AdsManagerUnified] 🚀 Инициализация единого менеджера рекламы...');
    
    // Основные свойства
    this.isInitialized = false;
    this.adsController = null;
    this.currentLanguage = 'ru';
    
    // Состояние системы
    this.state = {
      isAdShowing: false,
      lastAdShownTime: 0,
      cooldownTimers: new Map(),
      buttonStates: new Map(),
      userLimits: new Map()
    };
    
    // Кэш элементов DOM
    this.elements = new Map();
    
    // Event emitter для внутренних событий
    this.eventListeners = new Map();
  }
  
  /**
   * Главный метод инициализации
   */
  async init() {
    if (this.isInitialized) {
      console.warn('[AdsManagerUnified] Уже инициализирован');
      return;
    }
    
    try {
      console.log('[AdsManagerUnified] 🔧 Начинаем инициализацию...');
      
      // 1. Валидация конфигурации
      if (window.AdsConfig) {
        const validation = window.AdsConfig.validate();
        if (!validation.isValid) {
          throw new Error('Ошибки конфигурации: ' + validation.errors.join(', '));
        }
      }
      
      // 2. Инициализация DOM элементов
      this.initializeElements();
      
      // 3. Инициализация RichAds SDK
      await this.initializeRichAds();
      
      // 4. Загрузка лимитов пользователя
      await this.loadUserLimits();
      
      // 5. Настройка обработчиков событий
      this.setupEventListeners();
      
      // 6. Инициализация UI
      this.initializeUI();
      
      // Подписываемся на события смены языка
      this.setupLanguageChangeListener();

      this.isInitialized = true;
      console.log('[AdsManagerUnified] ✅ Инициализация завершена успешно');

      this.emit('manager:ready');
      
    } catch (error) {
      console.error('[AdsManagerUnified] ❌ Ошибка инициализации:', error);
      // Продолжаем работу в fallback режиме
      this.initializeFallbackMode();
    }
  }
  
  /**
   * БЕЗОПАСНОСТЬ: Fallback режим удален
   */
  initializeFallbackMode() {
    console.error('[AdsManagerUnified] ❌ Fallback режим отключен для безопасности');
    throw new Error('RichAds SDK недоступен - fallback режим заблокирован');
  }
  
  /**
   * Инициализация DOM элементов
   */
  initializeElements() {
    console.log('[AdsManagerUnified] 🔍 Поиск DOM элементов...');
    
    const adTypes = this.getAdTypesConfig();
    
    adTypes.forEach(adType => {
      const button = document.getElementById(adType.buttonId);
      const counter = document.getElementById(adType.counterId);
      
      if (!button) {
        console.warn(`[AdsManagerUnified] Кнопка не найдена: ${adType.buttonId}`);
        return;
      }
      
      this.elements.set(adType.id, {
        button,
        counter,
        adType
      });
      
      // Инициализируем состояние кнопки
      this.state.buttonStates.set(adType.id, 'ready');
    });
    
    console.log(`[AdsManagerUnified] ✅ Найдено ${this.elements.size} рекламных кнопок`);
  }
  
  /**
   * Получение конфигурации типов рекламы
   */
  getAdTypesConfig() {
    if (window.AdsConfig) {
      return window.AdsConfig.getAllAdTypes();
    }
    
    // Fallback конфигурация
    return [
      {
        id: 'native_banner',
        buttonId: 'openLinkButton',
        counterId: 'native-banner-counter',
        method: 'triggerInterstitialBanner',
        params: false,
        reward: 10
      },
      {
        id: 'rewarded_video',
        buttonId: 'watchVideoButton',
        counterId: 'rewarded-video-counter',
        method: 'triggerInterstitialVideo',
        params: null,
        reward: 1
      },
      {
        id: 'interstitial',
        buttonId: 'openAdButton',
        counterId: 'interstitial-counter',
        method: 'triggerInterstitialBanner',
        params: true,
        reward: 10
      }
    ];
  }
  
  /**
   * Инициализация RichAds SDK
   */
  async initializeRichAds() {
    console.log('[AdsManagerUnified] 📡 Инициализация RichAds SDK...');
    
    try {
      // БЕЗОПАСНОСТЬ: Только реальный TelegramAdsController
      if (!window.TelegramAdsController) {
        throw new Error('RichAds SDK не загружен');
      }

      const pubId = window.AdsConfig?.RICHADS?.PUB_ID || "944840";
      const appId = window.AdsConfig?.RICHADS?.APP_ID || "2122";

      // Инициализируем только реальный контроллер
      this.adsController = new window.TelegramAdsController();
      this.adsController.initialize({
        pubId,
        appId,
        debug: window.AdsConfig?.RICHADS?.DEBUG_MODE || false
      });
      
      if (!this.adsController) {
        throw new Error('Не удалось инициализировать RichAds контроллер');
      }
      
      console.log('[AdsManagerUnified] ✅ RichAds SDK инициализирован');
      
    } catch (error) {
      console.error('[AdsManagerUnified] ❌ Ошибка инициализации RichAds:', error);
      // Продолжаем работу без SDK (fallback режим)
      this.adsController = null;
    }
  }
  
  /**
   * Загрузка лимитов пользователя
   */
  async loadUserLimits() {
    console.log('[AdsManagerUnified] 📊 Загрузка лимитов пользователя...');
    
    try {
      const initData = this.getTelegramInitData();
      if (!initData) {
        console.warn('[AdsManagerUnified] InitData недоступен, используем тестовые лимиты');
        this.setTestLimits();
        return;
      }
      
      const response = await this.apiRequest('get_limits', {
        initData
      });
      
      if (response.success) {
        // Сохраняем лимиты в состоянии
        Object.entries(response.data.limits).forEach(([adType, limitInfo]) => {
          this.state.userLimits.set(adType, limitInfo);
        });
        
        console.log('[AdsManagerUnified] ✅ Лимиты пользователя загружены');
      } else {
        throw new Error(response.error || 'Ошибка загрузки лимитов');
      }
      
    } catch (error) {
      console.error('[AdsManagerUnified] ❌ Ошибка загрузки лимитов:', error);
      this.setTestLimits();
    }
  }
  
  /**
   * Установка тестовых лимитов
   */
  setTestLimits() {
    const dailyLimit = window.AdsConfig?.LIMITS?.DAILY_LIMIT_PER_TYPE || 20;
    
    this.getAdTypesConfig().forEach(adType => {
      this.state.userLimits.set(adType.id, {
        current: 0,
        limit: dailyLimit,
        remaining: dailyLimit,
        isLimitReached: false
      });
    });
  }
  
  /**
   * Настройка обработчиков событий
   */
  setupEventListeners() {
    console.log('[AdsManagerUnified] 🎯 Настройка обработчиков событий...');
    
    this.elements.forEach((element, adTypeId) => {
      if (element.button) {
        element.button.addEventListener('click', (event) => {
          event.preventDefault();
          this.handleAdButtonClick(adTypeId);
        });
      }
    });
    
    // Глобальная защита от случайных кликов
    this.setupGlobalClickProtection();
  }
  
  /**
   * Глобальная защита от кликов
   */
  setupGlobalClickProtection() {
    document.addEventListener('click', (event) => {
      if (this.state.isAdShowing) {
        // Проверяем, является ли цель рекламной кнопкой
        const isAdButton = Array.from(this.elements.values()).some(element => 
          element.button && (element.button === event.target || element.button.contains(event.target))
        );
        
        if (isAdButton) {
          event.preventDefault();
          event.stopPropagation();
          console.log('[AdsManagerUnified] 🛡️ Клик заблокирован - реклама показывается');
        }
      }
    }, true);
  }
  
  /**
   * Инициализация UI
   */
  initializeUI() {
    console.log('[AdsManagerUnified] 🎨 Инициализация UI...');
    
    // Определяем язык
    this.currentLanguage = this.detectLanguage();

    // ПРИНУДИТЕЛЬНО обновляем все счетчики с переводами
    this.forceUpdateAllCounters();

    // Обновляем состояние всех кнопок
    this.updateAllButtonStates();

    // Отложенное обновление заголовков (после localization.js)
    setTimeout(() => {
      this.updateAllButtonTitles();
      // Еще раз обновляем счетчики для гарантии
      this.forceUpdateAllCounters();
    }, 500);
  }
  
  /**
   * Определение языка пользователя
   */
  detectLanguage() {
    // Сначала пытаемся получить язык из системы локализации
    if (window.appLocalization && window.appLocalization.currentLanguage) {
      return window.appLocalization.currentLanguage;
    }

    // Пытаемся получить язык из Telegram
    const tg = window.Telegram?.WebApp;
    if (tg && tg.initDataUnsafe && tg.initDataUnsafe.user) {
      const langCode = tg.initDataUnsafe.user.language_code;
      if (langCode && langCode.startsWith('ru')) {
        return 'ru';
      }
    }

    // Fallback на браузерный язык
    const browserLang = navigator.language || navigator.userLanguage;
    return browserLang && browserLang.startsWith('ru') ? 'ru' : 'en';
  }
  
  /**
   * Обработка клика по рекламной кнопке
   */
  async handleAdButtonClick(adTypeId) {
    console.log(`[AdsManagerUnified] 🖱️ Клик по кнопке: ${adTypeId}`);
    
    try {
      const adType = this.getAdTypeConfig(adTypeId);
      if (!adType) {
        throw new Error(`Неизвестный тип рекламы: ${adTypeId}`);
      }
      
      // Логируем клик
      await this.logClick(adTypeId, 'button_click');
      
      // Проверяем возможность показа рекламы
      const canShow = this.canShowAd(adTypeId);
      if (!canShow.allowed) {
        console.warn(`[AdsManagerUnified] Показ рекламы заблокирован: ${canShow.reason}`);
        await this.logClick(adTypeId, canShow.logStatus, canShow.reason);
        this.showUserMessage(canShow.message);
        return;
      }
      
      // Показываем рекламу
      await this.showAd(adTypeId);
      
    } catch (error) {
      console.error(`[AdsManagerUnified] ❌ Ошибка обработки клика:`, error);
      this.showUserMessage('Произошла ошибка. Попробуйте позже.');
    }
  }
  
  /**
   * Получение конфигурации типа рекламы
   */
  getAdTypeConfig(adTypeId) {
    if (window.AdsConfig) {
      return window.AdsConfig.getAdType(adTypeId);
    }

    return this.getAdTypesConfig().find(type => type.id === adTypeId);
  }

  /**
   * Проверка возможности показа рекламы
   */
  canShowAd(adTypeId) {
    // Проверяем, не показывается ли уже реклама
    if (this.state.isAdShowing) {
      return {
        allowed: false,
        reason: 'Реклама уже показывается',
        message: 'Дождитесь завершения текущей рекламы',
        logStatus: 'ad_already_showing'
      };
    }

    // Проверяем cooldown
    if (this.isInCooldown(adTypeId)) {
      const remainingTime = this.getRemainingCooldownTime(adTypeId);
      return {
        allowed: false,
        reason: `Cooldown активен, осталось ${remainingTime}с`,
        message: `Подождите ${remainingTime} секунд`,
        logStatus: 'cooldown_active'
      };
    }

    // Проверяем лимиты
    const limitInfo = this.state.userLimits.get(adTypeId);
    if (limitInfo && limitInfo.isLimitReached) {
      return {
        allowed: false,
        reason: 'Достигнут дневной лимит',
        message: 'Дневной лимит просмотров исчерпан',
        logStatus: 'limit_exceeded'
      };
    }

    return { allowed: true };
  }

  /**
   * Показ рекламы
   */
  async showAd(adTypeId) {
    console.log(`[AdsManagerUnified] 📺 Показ рекламы: ${adTypeId}`);

    const adType = this.getAdTypeConfig(adTypeId);
    this.state.isAdShowing = true;

    try {
      // Обновляем UI
      this.setButtonState(adTypeId, 'loading');
      this.updateButtonText(adTypeId, 'Загрузка...');

      // Логируем запрос рекламы
      await this.logClick(adTypeId, 'ad_request', 'User requested ad show');

      // Показываем рекламу через RichAds
      if (this.adsController) {
        await this.showRichAd(adType);
      } else {
        // БЕЗОПАСНОСТЬ: Никаких fallback эмуляций
        throw new Error(`RichAds SDK недоступен - реклама заблокирована для ${adType.id}`);
      }

      // Обрабатываем успешный показ
      await this.handleAdSuccess(adTypeId);

    } catch (error) {
      console.error(`[AdsManagerUnified] ❌ Ошибка показа рекламы:`, error);
      await this.handleAdError(adTypeId, error);
    } finally {
      this.state.isAdShowing = false;
    }
  }

  /**
   * Показ рекламы через RichAds
   */
  async showRichAd(adType) {
    return new Promise((resolve, reject) => {
      const method = this.adsController[adType.method];
      if (!method) {
        reject(new Error(`Метод ${adType.method} не найден в RichAds SDK`));
        return;
      }

      // Устанавливаем таймаут
      const timeout = setTimeout(() => {
        reject(new Error('Таймаут показа рекламы'));
      }, window.AdsConfig?.LIMITS?.TIMEOUT || 30000);

      // Вызываем метод RichAds
      const adPromise = adType.params !== null
        ? method.call(this.adsController, adType.params)
        : method.call(this.adsController);

      adPromise
        .then((result) => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Fallback показ рекламы (УДАЛЕН ДЛЯ БЕЗОПАСНОСТИ)
   */
  async showFallbackAd(adType) {
    console.error(`[AdsManagerUnified] ❌ Fallback режим отключен для безопасности: ${adType.id}`);
    throw new Error(`RichAds SDK недоступен, fallback отключен для ${adType.id}`);
  }

  /**
   * Обработка успешного показа рекламы
   */
  async handleAdSuccess(adTypeId) {
    console.log(`[AdsManagerUnified] ✅ Успешный показ рекламы: ${adTypeId}`);

    try {
      // Записываем просмотр и начисляем награду
      const response = await this.apiRequest('record_view', {
        initData: this.getTelegramInitData(),
        adType: adTypeId
      });

      if (response.success) {
        // Обновляем баланс в UI
        if (window.appUtils && window.appUtils.updateBalance) {
          window.appUtils.updateBalance(response.data.newBalance);
        }

        // Показываем уведомление о награде
        this.showRewardNotification(response.data.reward);

        // Обновляем лимиты
        await this.updateUserLimits(adTypeId);

        // Запускаем cooldown
        this.startCooldown(adTypeId);

      } else {
        throw new Error(response.error || 'Ошибка записи просмотра');
      }

    } catch (error) {
      console.error(`[AdsManagerUnified] ❌ Ошибка обработки успеха:`, error);
      this.showUserMessage('Ошибка начисления награды');
    }
  }

  /**
   * Обработка ошибки показа рекламы
   */
  async handleAdError(adTypeId, error) {
    console.error(`[AdsManagerUnified] ❌ Ошибка показа рекламы ${adTypeId}:`, error);

    // Логируем ошибку
    await this.logClick(adTypeId, 'ad_error', error.message);

    // Восстанавливаем состояние кнопки
    this.setButtonState(adTypeId, 'ready');
    this.updateCounter(adTypeId);

    // Показываем сообщение пользователю
    this.showUserMessage('Реклама недоступна. Попробуйте позже.');
  }

  /**
   * Проверка активного cooldown
   */
  isInCooldown(adTypeId) {
    const cooldownEnd = this.state.cooldownTimers.get(adTypeId);
    return cooldownEnd && Date.now() < cooldownEnd;
  }

  /**
   * Получение оставшегося времени cooldown
   */
  getRemainingCooldownTime(adTypeId) {
    const cooldownEnd = this.state.cooldownTimers.get(adTypeId);
    if (!cooldownEnd) return 0;

    const remaining = Math.ceil((cooldownEnd - Date.now()) / 1000);
    return Math.max(0, remaining);
  }

  /**
   * Запуск cooldown таймера
   */
  startCooldown(adTypeId) {
    const cooldownTime = window.AdsConfig?.LIMITS?.COOLDOWN_TIME || 20000;
    const cooldownEnd = Date.now() + cooldownTime;

    this.state.cooldownTimers.set(adTypeId, cooldownEnd);
    this.setButtonState(adTypeId, 'cooldown');

    // Запускаем обратный отсчет
    this.startCountdown(adTypeId, cooldownTime / 1000);

    console.log(`[AdsManagerUnified] ⏱️ Cooldown запущен для ${adTypeId}: ${cooldownTime/1000}с`);
  }

  /**
   * Запуск обратного отсчета
   */
  startCountdown(adTypeId, seconds) {
    const updateCountdown = () => {
      const remaining = this.getRemainingCooldownTime(adTypeId);

      if (remaining <= 0) {
        // Cooldown завершен
        this.state.cooldownTimers.delete(adTypeId);
        this.setButtonState(adTypeId, 'ready');
        this.updateCounter(adTypeId);
        this.emit('cooldown:ended', { adTypeId });
        return;
      }

      // Обновляем текст кнопки
      this.updateButtonText(adTypeId, this.getCooldownText(remaining));

      // Продолжаем отсчет
      setTimeout(updateCountdown, 1000);
    };

    updateCountdown();
  }

  /**
   * Получение текста для cooldown
   */
  getCooldownText(seconds) {
    try {
      if (window.AdsConfig) {
        return window.AdsConfig.getCounterText(this.currentLanguage, 'cooldown', seconds);
      }

      if (window.appLocalization && window.appLocalization.isLoaded && window.appLocalization.t) {
        const text = window.appLocalization.t('app.status.wait_before_next');
        if (text && text !== 'app.status.wait_before_next') {
          return text.replace('{seconds}', seconds) || `подождите ${seconds}с`;
        }
      }
    } catch (error) {
      console.warn('[AdsManagerUnified] Ошибка получения текста cooldown:', error);
    }

    return this.currentLanguage === 'ru' ? `подождите ${seconds}с` : `wait ${seconds}s`;
  }

  /**
   * Установка состояния кнопки
   */
  setButtonState(adTypeId, state) {
    this.state.buttonStates.set(adTypeId, state);

    const element = this.elements.get(adTypeId);
    if (!element || !element.button) return;

    // Удаляем все классы состояний
    element.button.classList.remove('ready', 'loading', 'cooldown', 'disabled', 'limit_reached');

    // Добавляем новый класс состояния
    element.button.classList.add(state);

    // Управляем доступностью кнопки
    element.button.disabled = (state !== 'ready');
  }

  /**
   * Обновление текста кнопки (только счетчик)
   */
  updateButtonText(adTypeId, text) {
    const element = this.elements.get(adTypeId);
    if (!element || !element.counter) return;

    element.counter.textContent = text;
  }

  /**
   * Обновление заголовка кнопки с учетом локализации
   */
  updateButtonTitle(adTypeId) {
    const element = this.elements.get(adTypeId);
    if (!element || !element.button) return;

    try {
      let title = '';

      // Пытаемся получить перевод из системы локализации (приоритет)
      if (window.appLocalization && window.appLocalization.isLoaded && window.appLocalization.get) {
        const localizationKey = this.getLocalizationKeyForAdType(adTypeId);
        if (localizationKey) {
          const translatedTitle = window.appLocalization.get(localizationKey);
          if (translatedTitle && translatedTitle !== localizationKey) {
            title = translatedTitle;
            console.log(`[AdsManagerUnified] 🌐 Получен перевод для ${adTypeId}: "${title}" (ключ: ${localizationKey})`);
          }
        }
      }

      // Fallback на конфигурацию AdsConfig
      if (!title) {
        const adType = window.AdsConfig.getAdType(adTypeId);
        if (adType && adType.title) {
          title = adType.title[this.currentLanguage] || adType.title.ru || adType.title.en;
          console.log(`[AdsManagerUnified] 📝 Fallback заголовок для ${adTypeId}: "${title}"`);
        }
      }

      // Последний fallback - дефолтные значения
      if (!title) {
        const defaultTitles = {
          'native_banner': this.currentLanguage === 'ru' ? 'Открыть ссылку' : 'Open Link',
          'rewarded_video': this.currentLanguage === 'ru' ? 'Смотреть видео' : 'Watch Video',
          'interstitial': this.currentLanguage === 'ru' ? 'Открыть рекламу' : 'Tap and Earn'
        };
        title = defaultTitles[adTypeId] || 'Unknown';
        console.log(`[AdsManagerUnified] 🔧 Дефолтный заголовок для ${adTypeId}: "${title}"`);
      }

      // Обновляем текст в span.button-text
      const buttonTextSpan = element.button.querySelector('.button-text');
      if (buttonTextSpan && buttonTextSpan.textContent !== title) {
        buttonTextSpan.textContent = title;
        console.log(`[AdsManagerUnified] ✅ Обновлен заголовок кнопки ${adTypeId}: "${title}"`);
      }
    } catch (error) {
      console.warn(`[AdsManagerUnified] ❌ Ошибка обновления заголовка кнопки ${adTypeId}:`, error);
    }
  }

  /**
   * Получение ключа локализации для типа рекламы
   */
  getLocalizationKeyForAdType(adTypeId) {
    const keyMap = {
      'native_banner': 'tasks.open_link',
      'rewarded_video': 'tasks.watch_video',
      'interstitial': 'tasks.watch_ad'
    };
    return keyMap[adTypeId];
  }

  /**
   * Обновление счетчика
   */
  updateCounter(adTypeId) {
    const limitInfo = this.state.userLimits.get(adTypeId);
    if (!limitInfo) return;

    const text = this.getCounterText(limitInfo.remaining);
    this.updateButtonText(adTypeId, text);

    // Обновляем состояние кнопки если лимит достигнут
    if (limitInfo.isLimitReached) {
      this.setButtonState(adTypeId, 'limit_reached');
    }
  }

  /**
   * Получение текста счетчика - ПРЯМАЯ ВСТАВКА ПЕРЕВОДОВ
   */
  getCounterText(remaining) {
    // Определяем язык
    const isRussian = window.appLocalization?.currentLanguage === 'ru' ||
                     document.documentElement.lang === 'ru' ||
                     navigator.language.startsWith('ru');

    console.log(`[AdsManagerUnified] 🔍 Счетчик: remaining=${remaining}, русский=${isRussian}`);

    // ПРЯМАЯ ВСТАВКА переводов
    if (isRussian) {
      // Русский язык
      if (remaining === 0) return 'лимит исчерпан';
      if (remaining === 1) return 'остался 1 показ';
      if (remaining >= 2 && remaining <= 4) return `осталось ${remaining} показа`;
      return `осталось ${remaining} показов`;
    } else {
      // Английский язык
      if (remaining === 0) return 'limit reached';
      if (remaining === 1) return '1 ad view left';
      return `${remaining} ad views left`;
    }
  }

  /**
   * Обновление всех счетчиков
   */
  updateAllCounters() {
    this.elements.forEach((element, adTypeId) => {
      this.updateCounter(adTypeId);
    });
  }

  /**
   * ПРИНУДИТЕЛЬНОЕ обновление всех счетчиков с переводами
   */
  forceUpdateAllCounters() {
    console.log('[AdsManagerUnified] 🔄 ПРИНУДИТЕЛЬНОЕ обновление всех счетчиков...');

    this.elements.forEach((element, adTypeId) => {
      if (!element || !element.counter) return;

      // Получаем лимиты
      const limitInfo = this.state.userLimits.get(adTypeId);
      const remaining = limitInfo ? limitInfo.remaining : 20; // fallback

      // Получаем переведенный текст
      const text = this.getCounterText(remaining);

      // ПРЯМО устанавливаем текст
      element.counter.textContent = text;

      console.log(`[AdsManagerUnified] ✅ Счетчик ${adTypeId}: "${text}" (remaining: ${remaining})`);
    });
  }

  /**
   * Обновление всех заголовков кнопок
   */
  updateAllButtonTitles() {
    console.log('[AdsManagerUnified] 🔄 Обновление всех заголовков кнопок...');

    this.elements.forEach((element, adTypeId) => {
      this.updateButtonTitle(adTypeId);
    });
  }

  /**
   * Настройка слушателя смены языка
   */
  setupLanguageChangeListener() {
    // Слушаем события смены языка от системы локализации
    if (window.appLocalization) {
      // Проверяем изменения языка каждые 2 секунды
      setInterval(() => {
        const newLanguage = this.detectLanguage();
        if (newLanguage !== this.currentLanguage) {
          console.log(`[AdsManagerUnified] 🌐 Язык изменен с ${this.currentLanguage} на ${newLanguage}`);
          this.currentLanguage = newLanguage;
          this.updateAllButtonTitles();
          this.updateAllCounters();
        }
      }, 2000);
    }

    // Слушаем кастомные события смены языка
    document.addEventListener('language-changed', (event) => {
      const newLanguage = event.detail?.language || this.detectLanguage();
      if (newLanguage !== this.currentLanguage) {
        console.log(`[AdsManagerUnified] 🌐 Получено событие смены языка: ${newLanguage}`);
        this.currentLanguage = newLanguage;
        this.updateAllButtonTitles();
        this.updateAllCounters();
      }
    });
  }

  /**
   * Обновление состояния всех кнопок
   */
  updateAllButtonStates() {
    this.elements.forEach((element, adTypeId) => {
      const currentState = this.state.buttonStates.get(adTypeId) || 'ready';
      this.setButtonState(adTypeId, currentState);
    });
  }

  /**
   * Обновление лимитов пользователя после просмотра
   */
  async updateUserLimits(adTypeId) {
    try {
      const response = await this.apiRequest('get_limits', {
        initData: this.getTelegramInitData()
      });

      if (response.success) {
        Object.entries(response.data.limits).forEach(([adType, limitInfo]) => {
          this.state.userLimits.set(adType, limitInfo);
        });

        // Обновляем счетчики в UI
        this.updateAllCounters();
      }
    } catch (error) {
      console.error('[AdsManagerUnified] ❌ Ошибка обновления лимитов:', error);
    }
  }

  /**
   * Логирование клика
   */
  async logClick(adTypeId, clickType, reason = '') {
    try {
      const initData = this.getTelegramInitData();
      if (!initData) return;

      await this.apiRequest('log_click', {
        initData,
        adType: adTypeId,
        clickType,
        reason,
        timestamp: Date.now(),
        sessionId: this.getSessionId()
      });
    } catch (error) {
      console.error('[AdsManagerUnified] ❌ Ошибка логирования клика:', error);
    }
  }

  /**
   * API запрос
   */
  async apiRequest(action, data = {}) {
    const url = window.AdsConfig?.getApiUrl?.(action.toUpperCase()) || `./api/ads-api.php?action=${action}`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`[AdsManagerUnified] ❌ API ошибка (${action}):`, error);
      throw error;
    }
  }

  /**
   * Получение Telegram initData
   */
  getTelegramInitData() {
    const tg = window.Telegram?.WebApp;
    if (tg && tg.initData) {
      return tg.initData;
    }

    // БЕЗОПАСНОСТЬ: Никаких fallback данных
    throw new Error('Telegram initData недоступен - система заблокирована');
  }

  /**
   * Получение ID сессии
   */
  getSessionId() {
    if (!this.sessionId) {
      this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    return this.sessionId;
  }

  /**
   * Показ уведомления о награде
   */
  showRewardNotification(reward) {
    console.log(`[AdsManagerUnified] 🎉 Награда получена: +${reward} монет`);

    // Используем существующую систему уведомлений если доступна
    if (window.appUtils) {
      if (window.appUtils.showStatus) {
        window.appUtils.showStatus(`+${reward} монет получено!`, 'success');
      }
      if (window.appUtils.vibrate) {
        window.appUtils.vibrate('medium');
      }
    }

    this.emit('reward:received', { reward });
  }

  /**
   * Показ сообщения пользователю
   */
  showUserMessage(message, type = 'info') {
    console.log(`[AdsManagerUnified] 💬 Сообщение: ${message}`);

    if (window.appUtils && window.appUtils.showStatus) {
      window.appUtils.showStatus(message, type);
    } else {
      // Fallback
      console.warn(`[AdsManagerUnified] ${message}`);
    }
  }

  /**
   * Event emitter
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * Emit event
   */
  emit(event, data = {}) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[AdsManagerUnified] ❌ Ошибка в event listener (${event}):`, error);
        }
      });
    }
  }

  /**
   * Публичные методы для обратной совместимости
   */

  // Метод для ручного обновления лимитов
  async refreshLimits() {
    await this.loadUserLimits();
    this.updateAllCounters();
  }

  // Метод для получения состояния
  getState() {
    return {
      isInitialized: this.isInitialized,
      isAdShowing: this.state.isAdShowing,
      userLimits: Object.fromEntries(this.state.userLimits),
      buttonStates: Object.fromEntries(this.state.buttonStates)
    };
  }

  // Метод для принудительного сброса cooldown (для тестирования)
  resetCooldown(adTypeId) {
    if (adTypeId) {
      this.state.cooldownTimers.delete(adTypeId);
      this.setButtonState(adTypeId, 'ready');
      this.updateCounter(adTypeId);
    } else {
      this.state.cooldownTimers.clear();
      this.updateAllButtonStates();
      this.updateAllCounters();
    }
  }
}

// Создаем глобальный экземпляр
window.adsManagerUnified = new AdsManagerUnified();

// Экспорт для обратной совместимости
window.adsManager = window.adsManagerUnified;

// Экспорт основных функций для совместимости со старым кодом
window.initializeRichAds = () => window.adsManagerUnified.initializeRichAds();
window.handleWatchAdClick = () => window.adsManagerUnified.handleAdButtonClick('native_banner');
window.handleWatchVideoClick = () => window.adsManagerUnified.handleAdButtonClick('rewarded_video');
window.handleOpenLinkClick = () => window.adsManagerUnified.handleAdButtonClick('interstitial');
window.recordAdView = (adType) => window.adsManagerUnified.logClick(adType, 'manual_record');
window.refreshAds = () => window.adsManagerUnified.refreshLimits();

// Функция для принудительного обновления заголовков (для тестирования)
window.updateAdButtonTitles = () => {
  if (window.adsManagerUnified && window.adsManagerUnified.updateAllButtonTitles) {
    console.log('🔄 Принудительное обновление заголовков кнопок...');
    window.adsManagerUnified.updateAllButtonTitles();
    return true;
  }
  console.warn('❌ AdsManagerUnified не найден или не инициализирован');
  return false;
};

// Функция для принудительного обновления счетчиков
window.updateAdCounters = () => {
  if (window.adsManagerUnified && window.adsManagerUnified.forceUpdateAllCounters) {
    console.log('🔄 ПРИНУДИТЕЛЬНОЕ обновление счетчиков...');
    window.adsManagerUnified.forceUpdateAllCounters();
    return true;
  }
  console.warn('❌ AdsManagerUnified не найден или не инициализирован');
  return false;
};

// Функция для полного обновления (заголовки + счетчики)
window.updateAdButtons = () => {
  console.log('🔄 Полное обновление кнопок рекламы...');
  const titlesUpdated = window.updateAdButtonTitles();
  const countersUpdated = window.updateAdCounters();
  return titlesUpdated && countersUpdated;
};

// ПРЯМАЯ УСТАНОВКА переведенных счетчиков
window.forceSetCounters = () => {
  console.log('🔧 ПРЯМАЯ установка переведенных счетчиков...');

  // Определяем язык
  const isRussian = window.appLocalization?.currentLanguage === 'ru' ||
                   document.documentElement.lang === 'ru' ||
                   navigator.language.startsWith('ru');

  console.log('🌐 Язык русский:', isRussian);

  // Находим элементы счетчиков
  const counters = [
    { id: 'native-banner-counter', remaining: 20 },
    { id: 'rewarded-video-counter', remaining: 20 },
    { id: 'interstitial-counter', remaining: 20 }
  ];

  counters.forEach(({ id, remaining }) => {
    const element = document.getElementById(id);
    if (element) {
      let text;
      if (isRussian) {
        if (remaining === 1) text = 'остался 1 показ';
        else if (remaining >= 2 && remaining <= 4) text = `осталось ${remaining} показа`;
        else text = `осталось ${remaining} показов`;
      } else {
        text = remaining === 1 ? '1 ad view left' : `${remaining} ad views left`;
      }

      element.textContent = text;
      console.log(`✅ ${id}: "${text}"`);
    } else {
      console.warn(`❌ Элемент ${id} не найден`);
    }
  });

  return true;
};

// Функция для отладки переводов
window.debugTranslations = () => {
  console.log('🔍 Отладка системы переводов:');

  // Проверяем локализацию
  if (window.appLocalization) {
    console.log('✅ AppLocalization найден');
    console.log('📍 Текущий язык:', window.appLocalization.currentLanguage);
    console.log('📍 Загружена:', window.appLocalization.isLoaded);

    if (window.appLocalization.get) {
      console.log('📝 Тест переводов:');
      console.log('  tasks.open_link:', window.appLocalization.get('tasks.open_link'));
      console.log('  tasks.watch_video:', window.appLocalization.get('tasks.watch_video'));
      console.log('  tasks.watch_ad:', window.appLocalization.get('tasks.watch_ad'));
      console.log('  tasks.ad_views_left:', window.appLocalization.get('tasks.ad_views_left'));
    }
  } else {
    console.warn('❌ AppLocalization не найден');
  }

  // Проверяем AdsConfig
  if (window.AdsConfig) {
    console.log('✅ AdsConfig найден');
    const testText = window.AdsConfig.getCounterText('ru', 'remaining', 5);
    console.log('📝 Тест счетчика (5 показов):', testText);
  } else {
    console.warn('❌ AdsConfig не найден');
  }

  // Проверяем AdsManagerUnified
  if (window.adsManagerUnified) {
    console.log('✅ AdsManagerUnified найден');
    console.log('📍 Текущий язык:', window.adsManagerUnified.currentLanguage);
    console.log('📍 Инициализирован:', window.adsManagerUnified.isInitialized);
  } else {
    console.warn('❌ AdsManagerUnified не найден');
  }
};

console.log('📺 [AdsManagerUnified] Единый менеджер рекламы загружен и готов к работе');

// Экспорт для совместимости (без ES6 modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdsManagerUnified;
}
