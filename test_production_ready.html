<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Тест продакшен-готовой системы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .security-check {
            background: #1e3a1e;
            border: 1px solid #28a745;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .security-check h4 {
            color: #28a745;
            margin-top: 0;
        }
        .security-item {
            margin: 8px 0;
            padding: 5px;
        }
        .security-item.pass {
            color: #28a745;
        }
        .security-item.fail {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>🔒 Тест продакшен-готовой системы</h1>
    <p><strong>ВАЖНО:</strong> Эта страница проверяет систему БЕЗ эмуляций. Работает только в реальном Telegram WebApp.</p>

    <div class="test-section">
        <h3>🔍 Проверка системы безопасности</h3>
        <div id="securityCheck" class="security-check">
            <h4>🔒 Анализ безопасности системы:</h4>
            <div id="securityResults">Проверяем...</div>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 Состояние Telegram WebApp</h3>
        <div id="telegramStatus">Проверяем...</div>
        <button class="test-button" onclick="checkTelegramStatus()">🔄 Проверить статус</button>
    </div>

    <div class="test-section">
        <h3>🎯 Состояние RichAds SDK</h3>
        <div id="richAdsStatus">Проверяем...</div>
        <button class="test-button" onclick="checkRichAdsStatus()">🔄 Проверить RichAds</button>
    </div>

    <div class="test-section">
        <h3>📝 Лог системы</h3>
        <div id="testLog" class="log"></div>
        <button class="test-button" onclick="clearLog()">🗑️ Очистить лог</button>
    </div>

    <div id="statusMessage"></div>

    <!-- Загружаем только систему безопасности -->
    <script src="js/richads-security.js"></script>
    <script>
        // Настройка API
        window.API_BASE_URL = 'api';
        
        // БЕЗОПАСНОСТЬ: Никаких эмуляций Telegram WebApp
        
        function addToLog(message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        function checkSecuritySystem() {
            const resultsDiv = document.getElementById('securityResults');
            let html = '';
            
            // Проверяем отсутствие эмуляций
            const emulations = checkForEmulations();
            if (emulations.length === 0) {
                html += '<div class="security-item pass">✅ Эмуляции рекламы отсутствуют</div>';
            } else {
                html += '<div class="security-item fail">❌ Найдены эмуляции: ' + emulations.join(', ') + '</div>';
            }
            
            // Проверяем отсутствие тестовых пользователей
            const testUsers = checkForTestUsers();
            if (testUsers.length === 0) {
                html += '<div class="security-item pass">✅ Тестовые пользователи отсутствуют</div>';
            } else {
                html += '<div class="security-item fail">❌ Найдены тестовые пользователи: ' + testUsers.join(', ') + '</div>';
            }
            
            // Проверяем систему безопасности
            if (window.richAdsSecurityManager) {
                html += '<div class="security-item pass">✅ Система безопасности загружена</div>';
            } else {
                html += '<div class="security-item fail">❌ Система безопасности не найдена</div>';
            }
            
            // Проверяем защищенный API
            html += '<div class="security-item pass">✅ Защищенный API: secure_reward.php</div>';
            
            // Проверяем отсутствие fallback методов
            const fallbacks = checkForFallbacks();
            if (fallbacks.length === 0) {
                html += '<div class="security-item pass">✅ Fallback методы отсутствуют</div>';
            } else {
                html += '<div class="security-item fail">❌ Найдены fallback методы: ' + fallbacks.join(', ') + '</div>';
            }
            
            resultsDiv.innerHTML = html;
        }

        function checkForEmulations() {
            const emulations = [];
            
            // Проверяем глобальные объекты на наличие эмуляций
            if (window.adsController && window.adsController.emulated) {
                emulations.push('adsController.emulated');
            }
            
            // Проверяем методы на эмуляцию
            if (window.adsController && typeof window.adsController.triggerInterstitialBanner === 'function') {
                const methodStr = window.adsController.triggerInterstitialBanner.toString();
                if (methodStr.includes('emulated') || methodStr.includes('Promise.resolve')) {
                    emulations.push('triggerInterstitialBanner эмуляция');
                }
            }
            
            return emulations;
        }

        function checkForTestUsers() {
            const testUsers = [];
            
            // Проверяем на наличие тестовых ID
            if (window.currentUserId && (window.currentUserId === '12345' || window.currentUserId === 'debug_user_123' || window.currentUserId === 'test_user_123')) {
                testUsers.push(window.currentUserId);
            }
            
            // Проверяем Telegram данные на тестовые значения
            if (window.Telegram?.WebApp?.initDataUnsafe?.user?.id === 12345) {
                testUsers.push('Telegram user ID 12345');
            }
            
            return testUsers;
        }

        function checkForFallbacks() {
            const fallbacks = [];
            
            // Проверяем наличие fallback функций
            if (typeof window.showFallbackAd === 'function') {
                fallbacks.push('showFallbackAd');
            }
            
            if (window.adsManagerFull && typeof window.adsManagerFull.showFallbackAd === 'function') {
                const methodStr = window.adsManagerFull.showFallbackAd.toString();
                if (!methodStr.includes('throw new Error')) {
                    fallbacks.push('adsManagerFull.showFallbackAd');
                }
            }
            
            return fallbacks;
        }

        function checkTelegramStatus() {
            const statusDiv = document.getElementById('telegramStatus');
            let html = '<h4>📊 Статус Telegram WebApp:</h4>';
            
            // Проверяем наличие Telegram WebApp
            if (window.Telegram?.WebApp) {
                html += '<div class="security-item pass">✅ Telegram WebApp доступен</div>';
                
                // Проверяем initData
                if (window.Telegram.WebApp.initData) {
                    html += '<div class="security-item pass">✅ InitData присутствует</div>';
                } else {
                    html += '<div class="security-item fail">❌ InitData отсутствует</div>';
                }
                
                // Проверяем пользователя
                if (window.Telegram.WebApp.initDataUnsafe?.user) {
                    const user = window.Telegram.WebApp.initDataUnsafe.user;
                    html += `<div class="security-item pass">✅ Пользователь: ${user.first_name} (ID: ${user.id})</div>`;
                } else {
                    html += '<div class="security-item fail">❌ Данные пользователя недоступны</div>';
                }
            } else {
                html += '<div class="security-item fail">❌ Telegram WebApp недоступен</div>';
                html += '<div class="security-item warning">⚠️ Приложение должно работать только в Telegram</div>';
            }
            
            statusDiv.innerHTML = html;
        }

        function checkRichAdsStatus() {
            const statusDiv = document.getElementById('richAdsStatus');
            let html = '<h4>📊 Статус RichAds SDK:</h4>';
            
            // Проверяем загрузку RichAds SDK
            if (window.TelegramAdsController) {
                html += '<div class="security-item pass">✅ RichAds SDK загружен</div>';
            } else {
                html += '<div class="security-item fail">❌ RichAds SDK не загружен</div>';
            }
            
            // Проверяем контроллер рекламы
            if (window.adsManagerFull?.adsController) {
                html += '<div class="security-item pass">✅ Контроллер рекламы инициализирован</div>';
            } else {
                html += '<div class="security-item fail">❌ Контроллер рекламы не найден</div>';
            }
            
            statusDiv.innerHTML = html;
        }

        // Инициализация при загрузке
        window.addEventListener('load', () => {
            addToLog('🔒 Система тестирования продакшен-готовой версии загружена');
            checkSecuritySystem();
            checkTelegramStatus();
            checkRichAdsStatus();
            
            // Обновляем статус каждые 10 секунд
            setInterval(() => {
                checkSecuritySystem();
                checkTelegramStatus();
                checkRichAdsStatus();
            }, 10000);
        });
    </script>
</body>
</html>
