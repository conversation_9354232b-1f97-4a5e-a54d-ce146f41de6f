# Система статистики рекламных показов

## Описание
Развернутая система статистики рекламных показов для админ-панели UniQPaid с фильтрацией и детальной аналитикой.

## Новые файлы

### 1. `api/recordAdView.php` (обновлен)
- Добавлено логирование всех рекламных запросов
- Функция `logAdRequest()` для записи статистики
- Улучшенная функция `getCountryByIP()` с кэшированием
- Логирование статусов: request, success, empty, error, limit_exceeded, blocked_user

### 2. `api/admin/ad_statistics.php`
- Основная страница статистики рекламы
- Фильтры по дате, типу рекламы, статусу, стране
- Интерактивные графики (Chart.js)
- Экспорт данных в JSON

### 3. `api/admin/ad_stats_api.php`
- API для получения статистики
- Парсинг лог-файла `ad_requests.log`
- Агрегация данных по различным критериям
- Поддержка экспорта

### 4. Тестовые файлы
- БЕЗОПАСНОСТЬ: Все тестовые файлы удалены

### 5. `api/ad_requests.log`
- Лог-файл рекламных запросов (создается автоматически)
- JSON формат записей
- Защищен от прямого доступа через .htaccess

## Статистические показатели

### Общие метрики:
- Всего запросов рекламы
- Успешных показов
- Пустых запросов (нет рекламы)
- Ошибок
- Превышений лимита
- Процент успешности

### Детальная аналитика:
- Статистика по типам рекламы (native_banner, interstitial, rewarded_video)
- Статистика по странам (определение по IP)
- Статистика по дням и часам
- Топ пользователей по активности

### Фильтры:
- Период (дата с/по)
- Тип рекламы
- Статус запроса
- Страна

## Графики и визуализация

### 1. График по дням
- Линейный график показывающий динамику запросов и успешных показов
- Интерактивный с возможностью масштабирования

### 2. График по часам
- Столбчатая диаграмма активности по часам суток
- Помогает определить пиковые часы

### 3. Детальные таблицы
- Статистика по типам рекламы с процентами успешности
- Топ стран по количеству запросов
- Топ пользователей по активности

## Безопасность

### Защита файлов:
- `.htaccess` запрещает прямой доступ к `.log` файлам
- Защита от SQL инъекций в URL
- Аутентификация для доступа к админ-панели

### Логирование:
- Все действия логируются с IP адресами
- Определение страны по IP с кэшированием
- Защита от спама и злоупотреблений

## Использование

### Доступ к статистике:
1. Войти в админ-панель
2. Перейти в раздел "Статистика рекламы"
3. Настроить фильтры по необходимости
4. Просмотреть графики и таблицы

### Экспорт данных:
- Кнопка "Экспорт" для скачивания данных в JSON
- Включает как агрегированную статистику, так и сырые данные

### Создание тестовых данных:
```
http://your-domain.com/api/test_ad_stats.php
```
(только в режиме разработки)

## Структура лог-записи

```json
{
    "timestamp": 1749142419,
    "date": "2025-06-05 16:53:39",
    "user_id": 5880288830,
    "ad_type": "interstitial_banner_view",
    "status": "success",
    "ip": "**************",
    "user_agent": "Mozilla/5.0...",
    "country": "RU"
}
```

## Статусы запросов

- `request` - Запрос рекламы получен
- `success` - Реклама успешно показана, награда начислена
- `empty` - Нет доступной рекламы
- `error` - Ошибка при обработке
- `limit_exceeded` - Превышен лимит показов
- `blocked_user` - Заблокированный пользователь

## Интеграция

Система автоматически интегрирована с существующим кодом:
- Все вызовы `recordAdView.php` теперь логируются
- Новый пункт меню в админ-панели
- Совместимость с существующей системой безопасности

## Производительность

- Логирование оптимизировано для минимального влияния на производительность
- Кэширование IP-адресов для определения стран
- Эффективный парсинг лог-файлов с фильтрацией

## Расширение функциональности

Система легко расширяется:
- Добавление новых типов рекламы
- Интеграция с внешними GeoIP сервисами
- Дополнительные метрики и фильтры
- Уведомления при аномальной активности
